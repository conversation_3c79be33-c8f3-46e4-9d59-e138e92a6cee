// pagesA/searchresult/searchresult.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    value:'',
    list:[],
    finish: false,
    loading: false,
    page: 1,
    type: 1,
    is_price: '',
    is_rank: '',
    address: '',
    shape: 2
  },
  onLoadAddress(option){
    this.setData({
      address: app.globalData.address
    })
    this.reload()
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
   if(options){
    this.setData({
      value:options.value,
      type: options.type
    })
   }
  },
  listChange(e){
    let index = e.currentTarget.dataset.index
    this.setData({
      shape: index
    })
  },
  rankTap(e){
    let index = e.currentTarget.dataset.index
    if(index == 1){
      this.setData({
        is_price: '',
        is_rank: ''
      })
    }else if(index == 2){
      this.setData({
        is_price: '',
        is_rank: 1
      })
    }else if(index == 3){
      if(this.data.is_price === ''){
        this.setData({
          is_price: 1,
          is_rank: ''
        })
      }else if(this.data.is_price === 1){
        this.setData({
          is_price: 2,
          is_rank: ''
        })
      }else if(this.data.is_price === 2){
        this.setData({
          is_price: 1,
          is_rank: ''
        })
      }
    }
    this.reload()
  },
  serviceTap(e){
    util.skip('/service/servedetail/servedetail?id='+e.detail.id)
  },
  onChange(e) {
    this.setData({
      value: e.detail,
    });
  },
  onSearch(){
    if(this.data.value === '')  return util.toast('请输入搜索内容')
    let history = wx.getStorageSync('keyword'+this.data.type) || []
    let index = history.indexOf(this.data.value)
    if(index !== -1){
      history.splice(index,1)
    }
    history.unshift(this.data.value)
    wx.setStorageSync('keyword'+this.data.type,history)
    this.reload()
  },
  reload(){
    this.setData({
      list: [],
      page: 1,
      finish: false
    })
    this.getList()
  },
  more(){
    this.setData({
      page: ++this.data.page
    })
    this.getList()
  },
  getList(){
    if(this.data.finish){
      return 
    }
    if(this.data.loading){
      return 
    }
    this.setData({
      loading: true
    })
    http.get('goods/getlist', {
      page: this.data.page,
      name: this.data.value,
      is_rank:  this.data.is_rank,
      is_price: this.data.is_price,
      city: this.data.address.city
    }).then(res => {
      if(res.data.length === 0){
        this.setData({
          finish: true
        })
      }
      let arr = this.data.list.concat(res.data)
      this.setData({
        list: arr
      })
      
    }).finally(res => {
      this.setData({
        loading: false
      })
    })
  },
  lowerthreshold(){
    console.log('我到底了')
  },
  todetail(){
    wx.navigateTo({
      url: '/service/servedetail/servedetail',
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})