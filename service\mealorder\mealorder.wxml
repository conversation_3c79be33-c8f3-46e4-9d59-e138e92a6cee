<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="makeorder">
  <view class="main-box">
    <view class="mytop z-margin-t-32">
      <view class="nav_content">
        <view class="haveaddaddress z-margin-tb-32" wx:if="{{current==0 && address}}" bind:tap="toaddress">
          <view class="z-font-w">
            {{address.province}}{{address.city}}{{address.district}}{{address.area}}{{address.address}}</view>
          <view class="addressname z-margin-t-32">
            <view class="addressname_left text_999 z-font-24">
              <view class="z-margin-r-24">{{address.name}} {{address.sex === 1 ? '先生' : '先生'}}</view>
              <view>{{address.mobile}}</view>
            </view>
            <view class="addressname_right">
              <view class="z-font-24">修改地址</view>
              <van-icon name="arrow" />
            </view>
          </view>
        </view>
        <view class="haveaddaddress z-margin-tb-32" wx:elif="{{current==0}}" bind:tap="toaddress">
          <view class="z-flex z-padding-40">
            <image src="../../static/service/addaddress.png" class="add"></image>
            <view class="z-font-30 text_999">选择地址</view>
          </view>
        </view>
 
        <van-divider />
        <view class="servetime z-margin-b-16"  bindtap="chooseTime">
          <view class="z-font-22">服务时间</view>
          <view class="time z-font-24">
            <view>{{tool.formatTime(timeInfo.starttime)}}</view>
            <van-icon name="arrow" color="#999" size="24rpx" />
          </view>
        </view>
      </view>
    </view>

    <view class="goodsinfo z-padding-24 z-radius-16 z-margin-tb-32">
      <view class="goodsinfo_top">
        <image src="{{tool.cdn(info.image)}}" class="goodsimg z-radius-16 z-margin-r-16" mode="aspectFill" />
        <view class="goodsinfo_top_right">
          <view class="goodsinfo_top_right_top">
            <view class="tip  z-flex  z-margin-r-8 z-font-18" wx:if="{{info.shop_id == 0}}">严选</view>
            <view class="z-font-w">{{name}}</view>
          </view>
          <view class="remain">剩余{{remain}}次</view>
         
          <view class="goodsinfo_top_right_bottom">
            <view class="goodsinfo_top_right_bottom_left">
              <view class="goodsinfo_top_right_bottom_left_left">
  
              </view>
            </view>
            <view class="goodsinfo_top_right_bottom_right">
              <van-stepper theme='round' value="{{ num }}" bind:change="numChange" />
            </view>
          </view>
        </view>
      </view>

    </view>

    <view class="order_bz z-margin-tb-32 z-padding-24 z-radius-16">
      <view class="z-margin-b-32">
        订单备注
      </view>
      <textarea value="{{memo}}" class="memo" bindinput="setMemo" maxlength='-1' auto-height="true"
        placeholder="填写备注信息" />
    </view>
   
  </view>
  <view class="bottom-box" >

    <view class="order_bottom z-padding-lr-32 z-padding-t-32" style="padding-bottom: {{safeBottom}}">


      <view class="order_bottom_right z-raius-24" bindtap="repay">
        <van-button type="primary" color="#1782fa" round size='large'> 立即预约</van-button>
      </view>
    </view>
  </view>

  <van-popup show="{{ show }}" position="bottom" bind:close="onClose">
    <view class="time-box">
      <view class="z-font-32 z-padding-24 z-text-c">选择服务时间</view>
      <view class="z-flex-c z-marigin-32">
        <block wx:for="{{dayTime}}">
          <view class="z-flex-1 z-flex-y-c {{index == day ?  'today' : ''}}" data-index="{{index}}" bindtap="changeDay">
            <view class="day-t z-font-30">{{item.day}}</view>
            <view class="day-b z-font-22">{{item.time}}</view>
          </view>
        </block>

      </view>
      <view class="times-box  z-padding-32">
        <block wx:for="{{list}}">

            <view
              wx:if="{{(item.starttime - now) >=0 && tool.getHours(item.starttime) >= info.start_hour && tool.getHours(item.starttime) < info.end_hour}}"
              class="times z-radius-24 {{item.state === 1 ? 'active' : '' }}  {{item.state === 2 ? 'disable' : '' }} {{item.starttime == timeInfo.starttime ? 'choosed' : '' }}"
              bindtap="choose" data-info="{{item}}">
              <view class="times-t z-font-40 z-font-w">{{tool.formatTime(item.starttime, 'HH:MM')}}</view>
              <view class="times-b z-font-22" wx:if="{{item.starttime == timeInfo.starttime}}">当前选择</view>
              <view class="times-b z-font-22" wx:elif="{{item.state === 1}}">已预约</view>
              <view class="times-b z-font-22" wx:elif="{{item.state === 2}}">不可预约</view>

              <view class="times-b z-font-22" wx:else>可预约</view>
            </view>

        </block>
        <view wx:if="{{day == 0 && today == 1}}" class="z-flex-c text_666 z-font-24 z-margin-t-72 notime">今日暂无可约时段
        </view>
      </view>
      <view class="z-btn z-margin-32" bindtap="onClose">确定</view>
    </view>
  </van-popup>
</view>