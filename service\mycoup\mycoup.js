// service/mycoup/mycoup.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    active:0,
    state: 0,
    list:[],
    finish: false,
    loading: false,
    page: 1,
    goods: ''
  },
  couponTap(e){
    if(this.data.goods){
      const eventChannel = this.getOpenerEventChannel()
      eventChannel.emit('selectCoupon',e.detail);
      util.back();
    }else if(e.detail.type == 2){
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
      
  },
  onChange(e){
    this.setData({
      state: e.detail.name
    })
    this.reload()
  },
  reload(){
    this.setData({
      list: [],
      page: 1,
      finish: false
    })
    this.getList()
  },
  more(){
    this.setData({
      page: ++this.data.page
    })
    this.getList()
  },
  getList(){
    if(this.data.finish){
      return 
    }
    if(this.data.loading){
      return 
    }
    this.setData({
      loading: true
    })
    http.get('coupon/usercoupon', {
      page: this.data.page,
      state: this.data.state
    }).then(res => {
      if(res.data.length === 0){
        this.setData({
          finish: true
        })
      }
      let arr = this.data.list.concat(res.data)
      this.setData({
        list: arr
      })
      
    }).finally(res => {
      this.setData({
        loading: false
      })
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    
    this.setData({
      goods: options.goods_id ? options : ''
    })
    this.reload()
  },

})