/* service/login/login.wxss */
page{
  background-color: #fff;
  position: relative;
}
.logo_img{
  margin-top: 100rpx;
  width: 215rpx;
  height: 215rpx;
}
.title{
  font-size: 50rpx;
  margin-top: 200rpx;
}
.money{
  color: var(--maingreencolor);
}
/* van-radio 样式调整 */
.van-radio {
  align-items: flex-start;
}

.van-radio__icon {
  margin-top: 4rpx;
}
.btn-box{
  position: relative;
}
.btn{
  width: 686rpx;
  height: 98rpx;
  box-sizing: border-box;
  border: none;
  color: #fff;
  background: #1782FA;
  padding: 30rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 76rpx;
  font-size: 30rpx;
}
.mask{
  position: absolute;
  left: 0;
  top: 0;
  width: 686rpx;
  height: 98rpx;
}


.info-box{
  background-color: #F5F7F9;
}

.avatar-box{
  position: relative;
}


.avatar{
  width: 68rpx;
  height: 68rpx;
}

/* 导航栏样式 */
.van-nav-bar {
  background-color: #c8e2ff !important;
  z-index: 999;
}

.van-nav-bar__title {
  font-family: PingFangSC, PingFang SC !important;
  font-weight: 600 !important;
  font-size: 34rpx !important;
  color: #333333 !important;
  text-align: center !important;
  font-style: normal !important;
}

.van-nav-bar__arrow {
    color: #333333 !important;
    font-size: 44rpx !important;
    vertical-align: middle;
}

.van-nav-bar .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

.van-nav-bar__left .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}
