// pagesA/servedetail/servedetail.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"

Page({

  /**
   * 页面的初始数据
   */
  data: {
    current: 0,
    show: false,
    id: '',
    info: '',
    goods_sku_id: '',
    num: 1,
    skill_id: '',
    shop_id: '',
    safeBottom: `30px`,
    list: [],
    commentInfo: '',
    price: ''
  },
  previewImages(e){
    util.previewImage(this.data.info.images, e.currentTarget.dataset.index)
  },
  previewIll(e){
    util.previewImage(this.data.info.illustrate_images, e.currentTarget.dataset.index)
  },
  onClose() {
    this.setData({
      show: false
    });
  },
  collect(){
    if(!wx.getStorageSync('token')) {
      util.skip('/service/login/login')
      return
    }
    http.get('follow/handlefollow', {
      follow_id: this.data.id,
      type: 1,
      state: this.data.info.followState ? 0 : 1
    }).then(res => {
      this.data.info.followState = this.data.info.followState ? 0 : 1
      this.setData({
        info: this.data.info
      })
    })
  },
  share(){
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },
  onClickLeft() {
    wx.navigateBack()
  },
  selectSpu(e){
    let index = e.currentTarget.dataset.index
    let item = e.currentTarget.dataset.item
    this.data.info.spu[index].select = item
    this.setData({
      info: this.data.info
    })
    let arr = this.data.info.spu.map(value => {
      return value.select
    })
    arr = arr.join(',')
    for(let i=0; i< this.data.info.sku.length; i++){
      if(this.data.info.sku[i].name == arr){
        this.setData({
          goods_sku_id: this.data.info.sku[i].id,
          price:this.data.info.sku[i].price
        })
        break
      }
    }
  },
  chooseSku(e) {
    this.setData({
      goods_sku_id: e.currentTarget.dataset.id,
      price: e.currentTarget.dataset.price
    })
  },
  add() {
    this.setData({
      num: ++this.data.num
    })
  },
  sub() {
    if (this.data.num === 1) {
      return
    }
    this.setData({
      num: ++this.data.num
    })
  },
  makeorder() {
    if(!wx.getStorageSync('token')) {
      util.skip('/service/login/login')
      return
    }
    this.setData({
      show: false
    })
    if (this.data.skill_id) {
      util.skip('/service/makeorder/makeorder?goods_sku_id=' + this.data.goods_sku_id + '&num=' + this.data.num + '&goods_id=' + this.data.id + '&skill_id=' + this.data.skill_id+'&shop_id='+this.data.shop_id)

    } else if (this.data.info.choose_skill_type === 1){
      util.skip('/service/servelist/servelist?goods_sku_id=' + this.data.goods_sku_id + '&num=' + this.data.num + '&goods_id=' + this.data.id+'&shop_id='+this.data.shop_id)
    } else {
      util.skip('/service/makeorder/makeorder?goods_sku_id=' + this.data.goods_sku_id + '&num=' + this.data.num + '&goods_id=' + this.data.id+'&shop_id='+this.data.shop_id)
    }


    

  },
  openshow() {
   
    if(this.data.info.spec_type === 1 && this.data.info.sku.length > 0){
      this.setData({
        show: true
      });
    }else {
      if(!wx.getStorageSync('token')) {
        util.skip('/service/login/login')
        return
      }
      if (this.data.skill_id) {
        util.skip('/service/makeorder/makeorder?goods_sku_id=' + this.data.goods_sku_id + '&num=' + this.data.num + '&goods_id=' + this.data.id + '&skill_id=' + this.data.skill_id+'&shop_id='+this.data.shop_id)
  
      } else if (this.data.info.choose_skill_type === 1){
        util.skip('/service/servelist/servelist?goods_sku_id=' + this.data.goods_sku_id + '&num=' + this.data.num + '&goods_id=' + this.data.id+'&shop_id='+this.data.shop_id)
      } else {
        util.skip('/service/makeorder/makeorder?goods_sku_id=' + this.data.goods_sku_id + '&num=' + this.data.num + '&goods_id=' + this.data.id+'&shop_id='+this.data.shop_id)
      }
      
    }
    
  },
  comment(){
    util.skip('/service/comments/comments?type=goods&id='+this.data.id)
  },
  level(){
    util.authSkip('/service/level/level')
  },
  preimg() {
    wx.previewImage({
      current: '../../static/index/bg.png',
      urls: ['../../static/index/bg.png'],
    })
  },


  bindchange(event) {
    this.setData({
      current: event.detail.current
    })
  },
  getInfo() {
    http.get('goods/goodsinfo', {
      id: this.data.id
    }, true).then(res => {
      res.data.spu = res.data.spu.map(value => {
        value.skudetail = value.skudetail.split(',')
        value.select = value.skudetail[0]
        return value
      })
      this.setData({
        info: res.data,
        goods_sku_id: res.data.sku[0] ? res.data.sku[0].id : '',
        price: res.data.sku[0] ? res.data.sku[0].price : '',
      })
    })
    http.get('comment/getlist', {
      goods_id: this.data.id,
    }).then(res => {
      this.setData({
        list: res.data,
      })
    })
    http.get('comment/totalcomment', {
      goods_id: this.data.id,
      type: 'goods'
    }).then(res => {
      this.setData({
        commentInfo: res.data
      })
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const high = app.globalData.safeBottom
    this.setData({
      safeBottom: `${high}px`
    })
    this.setData({
      id: options.id,
      skill_id: options.skill_id || '',
      shop_id: options.shop_id || ''
    })
    this.getInfo()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('我下拉了')
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    console.log('我下拉了')
  }
})