<!--service/myorder/myorder.wxml-->

<view class="myorder">
  <view class="top-box">
    <van-tabs wx:if="{{type == 1}}" active="{{ active }}" swipeable='{{true}}' ellipsis='{{false}}' dot="true" info='1' color='#1782FA'
      animated="{{true}}" bind:change="onChange">
      <van-tab title="全部"></van-tab>
      <van-tab title="待付款"></van-tab>
      <van-tab title="待服务/待核销"></van-tab>
      <van-tab title="服务中"></van-tab>
      <van-tab title="已完成"></van-tab>
    </van-tabs>
    <van-tabs wx:if="{{type == 2}}" active="{{ active }}" swipeable='{{true}}' ellipsis='{{false}}' dot="true" info='1' color='#1782FA'
      animated="{{true}}" bind:change="onChange">
      <van-tab title="待评价"></van-tab>
      <van-tab title="已评价"></van-tab>
    </van-tabs>
    <van-tabs wx:if="{{type == 3}}" active="{{ active }}" swipeable='{{true}}' ellipsis='{{false}}' dot="true" info='1' color='#1782FA'
      animated="{{true}}" bind:change="onChange">
      <van-tab title="售后中"></van-tab>
      <van-tab title="已退款"></van-tab>
      <van-tab title="已拒绝"></van-tab>
    </van-tabs>
  </view>
  <view class="main-box">
    <scroll-view class="z-padding-32 scroll-box" scroll-y="true" show-scrollbar="{{false}}" enhanced="true"
      bindscrolltolower="more">
      <block wx:for="{{list}}" wx:key="item.id">
        <order info="{{item}}" type="{{2}}" bind:orderTap="orderTap" bind:orderBtnTap="orderBtnTap" bind:orderShopTap="orderShopTap"></order>
      </block>
      <van-empty description="暂无订单" wx:if="{{finish && list.length === 0}}" />
    </scroll-view>
  </view>
</view>


<van-popup show="{{ show }}" round closeable bind:close="onClose">
  <view class="popbox z-padding-32">
    <view class="z-flex z-font-w z-font-30">补差价</view>
    <view class="input_box z-padding-24 z-radius-24 z-margin-tb-32">
      <view class="z-font-30">补差价金额</view>
      <input placeholder="￥0.00" type="digit" value="{{price}}" bindinput="setPrice" />
    </view>
    <view class="z-font-22 text_999">*请与服务人员协商一致后再支付，如有异议请联系平台客服。平台仅对在线支付的项目提供交易保障，请勿线下支付</view>
    <van-divider />
    <view class="popbox_bottom">
      <view class="popbox_bottom_money">
        总计：
        <view class="money z-font-w">￥{{price}}</view>
      </view>
      <van-button bind:tap="uppay" type="primary" color="#1782fa" round>立即支付</van-button>
    </view>
  </view>
</van-popup>
<van-popup show="{{ addShow }}" round closeable bind:close="onaddClose">
  <view class="popbox z-padding-32">
    <view class="z-flex z-font-w z-margin-b-24">加项</view>
    <scroll-view scroll-y="true" show-scrollbar="{{false}}" enhanced="true" style="height: 700rpx;"
      bindscrolltolower="pluscd">
      <view class="plus_item z-padding-24 z-radius-24 z-margin-b-24" wx:for="{{addList}}" wx:key="*this">
        <view class="plus_item_left">
          <view class="z-font-w z-font-26">{{item.name}}</view>
          <view class="z-margin-t-16"><text class="money z-font-26">￥{{item.price}}</text><text
              class="z-font-22">/{{item.cost_seconds}}分钟</text></view>
        </view>
        <van-stepper theme='round' min="{{0}}" integer value="{{ item.num }}" data-index="{{index}}" bind:change="plusonChange" />
      </view>
    </scroll-view>
    <view class="text_999 z-font-22 z-margin-tb-32">
      *请与服务人员协商一致后再支付，如有异议请联系平台客服。平台仅对在线支付的项目提供交易保障，请勿线下支付
    </view>
    <van-divider />
    <view class="popbox_bottom">
      <view class="popbox_bottom_money">
        总计：
        <view class="money z-font-w">￥{{allprice}}</view>
      </view>
      <van-button type="primary" color="#1782fa" round bindtap="addpay">立即支付</van-button>
    </view>
  </view>
</van-popup>