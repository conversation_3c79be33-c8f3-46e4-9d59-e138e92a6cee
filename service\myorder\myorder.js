// service/myorder/myorder.js
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    show:false,
    addShow:false,
    active: 0,
    status: '',
    list:[],
    finish: false,
    loading: false,
    page: 1,
    price: '',
    order_id: '',
    type: 1,
    dot: false,
    addList: [],
    allprice: ''
  },

  onChange(e){
    this.setData({
      active: e.detail.index
    })
    this.reload()
  },
  plusonChange(event) {
    let index= event.currentTarget.dataset.index
    this.data.addList[index].num = event.detail
    let allprice = 0;
    for(let i=0; i < this.data.addList.length; i++){
      allprice += this.data.addList[i].num * this.data.addList[i].price
    }
    this.setData({
      addList: this.data.addList,
      allprice:allprice
    })
  },
  addpay(){
    let add_ids = [];
    for(let i=0; i < this.data.addList.length; i++){
      if(this.data.addList[i].num > 0){
        add_ids.push(this.data.addList[i].id +'-'+ this.data.addList[i].num)
      }
    }
    if(add_ids.length === 0) return util.toast('请选择需要加的项目')
    this.setData({
      addShow: false
    })
    wx.navigateTo({
      url: '/service/paypage/paypage?add_ids='+add_ids.join(',')+'&order_id='+this.data.order_id+'&type=2'+'&price='+this.data.allprice,
    })
  },
  uppay(){
    if(!this.data.price) return util.toast('请输入差价金额')
    this.setData({
      show: false
    })
    wx.navigateTo({
      url: '/service/paypage/paypage?price='+this.data.price+'&order_id='+this.data.order_id+'&type=3',
    })
  },
  onaddClose(){
    this.setData({
      addShow:false,
      allprice: 0
    })
  },
  onClose(){
    this.setData({
      show:false
    })
  },
  
  orderTap(e){
    util.skip('/service/orderdetail/orderdetail?id='+e.detail.id)
  },
  setPrice(e){
    this.setData({
      price: e.detail.value
    })
  },
  orderShopTap(e){
    util.skip('/service/shopdetail/shopdetail?shop_id='+e.detail.shop_id)
  },
  orderBtnTap(e){
    if(e.detail.type == 3){
      this.setData({
        show: true,
        order_id: e.detail.info.id
      })
    }else if(e.detail.type == 2){
      http.get('goods/goodsaddlist',{
        id: e.detail.info.orderDetail.goods_id
      }).then(res => {
        if(res.data.length === 0){
          util.toast('该项目暂无可加的项目')
        }else{
          let arr = res.data.map(value => {
            value.num = 0
            return value
          })
          this.setData({
            addShow: true,
            addList: arr,
            order_id: e.detail.info.id
          })
        }
        
      })
      
    }else if(e.detail.type == 1){
      util.skip('/service/applyrefund/applyrefund?order_id='+e.detail.info.id,{
        'applyRefund': ()=>{
          this.reload()
        }
      })
    }else if(e.detail.type == 4){
      util.skip('/service/paypage/paypage?price='+e.detail.info.payprice+'&order_id='+e.detail.info.id+'&type=1')
    }else if(e.detail.type == 5){
      util.skip('/service/gocomment/gocomment?order_id='+e.detail.info.id)
    }else if(e.detail.type == 6){
      util.skip('/service/ordercomments/ordercomments?order_id='+e.detail.info.id)
    }
  },
  reload(){
    this.setData({
      list: [],
      page: 1,
      finish: false
    })
    this.getList()
  },
  more(){
    this.setData({
      page: ++this.data.page
    })
    this.getList()
  },
  getList(){
    if(this.data.finish){
      return 
    }
    if(this.data.loading){
      return 
    }
    this.setData({
      loading: true
    })
    let data = {
      page: this.data.page,
    }
    if(this.data.type == 1){
      if(this.data.active === 1){
        data['status'] = 0
      }else if(this.data.active === 2){
        data['not_in_finish'] = 1
      }else if(this.data.active === 3){
        data['status'] = 5
      }else if(this.data.active === 4){
        data['finish'] = 1
      }else if(this.data.active === 0){
        data['is_all'] = 1
      }
    }else if(this.data.type == 2){
      if(this.data.active === 0){
        data['status'] = 6
      }else if(this.data.active === 1){
        data['status'] = 7
      }
    }else if(this.data.type == 3){
      if(this.data.active === 0){
        data['is_service'] = 1
      }else if(this.data.active === 1){
        data['is_service'] = 2
      }else if(this.data.active === 2){
        data['is_service'] = -1
      }
    }
    http.get('order/userorderlist', data).then(res => {
      if(res.data.length === 0){
        this.setData({
          finish: true
        })
      }
      let arr = this.data.list.concat(res.data)
      this.setData({
        list: arr
      })
      
    }).finally(res => {
      this.setData({
        loading: false
      })
    })
  },
  onLoad(options) {
    this.setData({
      type: options.type ? Number(options.type) : 1,
      active: options.active ?  Number(options.active) : 0,
      dot: options.num > 0
    })
    
    
  },
  onShow(){
    this.reload()
  }
  
})