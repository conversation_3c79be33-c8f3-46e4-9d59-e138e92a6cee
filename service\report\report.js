// service/report/report.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    columns: [],
    content: '',
    complaint_content: [],
    images: [],
    id: '',
    state: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      id: options.order_id,
    })
    this.getInfo()
    this.getList()
  },
  // 导航栏返回处理
  onClickLeft(){
    util.back()
  },
  getInfo(){
    http.get('/order/complaintInfo',{
      order_id: this.data.id
    }).then(res => {
      if(res.data){
        this.setData({
          state: res.data.state,
          complaint_content: res.data.complaint_content.split(','),
          content: res.data.content,
          images: res.data.images === '' ? [] : res.data.images,
        })
      }
      
    })
  },
  resave(){
    wx.requestSubscribeMessage({
      tmplIds: [app.globalData.templateconfig.user_complaint_template],
      complete :(res) =>{ 
        this.save()
      }
    })
  },
  save(){
    if (this.data.complaint_content.length === 0) return util.toast('请选择举报原因')
    http.post('order/complaint',{
      order_id:  this.data.id,
      complaint_content:  this.data.complaint_content.join(','),
      content:  this.data.content,
      images: this.data.images.join(',')
    }, true).then(res => {
      util.toast(res.msg)
      setTimeout(()=>{
        util.back();
      }, 1000)
    })
  },
  getList(){
    http.get('order/complaintreason').then(res => {
      this.setData({
        columns: res.data
      })
      
    })
  },
  chooseImage(e){
    let index = e.currentTarget.dataset.index
    http.chooseImg(['album', 'camera'], true, true, this.data.token).then(res => {
      let arr = this.data.images;
      if(index !== undefined){
        arr.splice(index,1,res.data.url)
      }else {
        arr.push(res.data.url)
      }
      this.setData({
        images: arr
      })
    })
  },
  delImages(e){
    let index = e.currentTarget.dataset.index
    this.data.images.splice(index,1)
    this.setData({
      images: this.data.images
    })
  },
  setContent(e){
    this.setData({
      content: e.detail.value
    })
  },
  choose(e) {
    let current = e.currentTarget.dataset.name
    if (this.data.complaint_content.includes(current)) {
      this.data.complaint_content = this.data.complaint_content.filter(item => item != current)
    } else {
      this.data.complaint_content.push(current)
    }
    this.setData({
      complaint_content: this.data.complaint_content
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})