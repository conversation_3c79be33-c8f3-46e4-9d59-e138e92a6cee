<!--index.wxml-->

<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="index_box">
  <view class="index_top z-padding-b-24">
    <view class="index_top_location z-margin-lr-32"
      style="padding-top:{{safeTop}};height: {{menuH}};box-sizing: content-box;" bindtap="selectAddress">
      <image class="location_img" src="../../static/index/location.png" mode="" />
      <view class="location_text z-margin-l-16 z-margin-r-8 z-font-32">{{address.name}}</view>
      <image src="/static/index/more.png" class="more"></image>
    </view>
  </view>
  <view class="index_bottom">
    <scroll-view class="index_main" scroll-y="true">
      <!-- 搜索 -->
      <view class="search_box z-radius-40 z-margin-lr-32 z-padding-lr-24" bindtap="search">
        <image class="search_img" src="../../static/index/search.png" mode="" />
        <view class="search_word z-font-24 text_999 z-margin-l-24">搜索上门服务</view>
      </view>
      <!-- 轮播图 -->
      <swiper class="swiper_box" autoplay="true">
        <swiper-item wx:for="{{thumb}}" class="swiper_img ">
          <image class="swiper_img" src="{{tool.cdn(item.image)}}" mode="aspectFill" data-item="{{item}}"
            bindtap="swiperTap" />
        </swiper-item>
      </swiper>
      <!-- 公告 -->
      <view class="z-flex-c-s-b z-padding-lr-24 z-radius-16 notice-box" wx:if="{{config.user_notice}}">
        <image class="notice-img" src="../../static/index/indexnotice.png"></image>
        <van-notice-bar class="z-flex-1" color="#6B738B" background="#fff" text="{{config.user_notice}}" />
      </view>
      <view class="index_content">
        <!-- 导航 -->
        <view class="ten_btn  z-radius-20">
          <view class="ten_btn_box z-radius-20 z-padding-t-16">
            <view class="ten_btn_item z-padding-tb-24  z-flex-y-c" bindtap="cateList" wx:for="{{cate}}" wx:key="id"
              data-item="{{item}}">
              <image src="{{tool.cdn(item.image)}}" class="ten_btn_img" mode="aspectFill" />
              <view class="z-font-24">{{item.label}}</view>
            </view>
          </view>
          <view class="ten_btn_bottom z-padding-24 ">
            <view class="ten_btn_bottom_item">
              <image src="../../static/index/one.png" class="ten_btn_bottom_img" mode="" />
              <view class="z-font-22 z-padding-l-8">全场保障</view>
            </view>
            <view class="ten_btn_bottom_item">
              <image src="../../static/index/two.png" class="ten_btn_bottom_img" mode="" />
              <view class="z-font-22 z-padding-l-8">7×24小时服务</view>
            </view>
            <view class="ten_btn_bottom_item">
              <image src="../../static/index/three.png" class="ten_btn_bottom_img" mode="" />
              <view class="z-font-22 z-padding-l-8">不满意重做</view>
            </view>
            <view class="ten_btn_bottom_item">
              <image src="../../static/index/four.png" class="ten_btn_bottom_img" mode="" />
              <view class="z-font-22 z-padding-l-8">迟到必赔</view>
            </view>
          </view>
        </view>
        
        <!-- 附近服务者 -->
        <view class="index_samebox" wx:if="{{nearSkill.length>0}}">
          <view class="index_samebox_top z-margin-tb-30">
            <view class="z-font-26 z-font-w">附近服务者</view>
            <view class="z-font-24 text_999" bindtap="nearSkill">更多
              <van-icon name="arrow" />
            </view>
          </view>
        </view>

        <scroll-view scroll-x="true" show-scrollbar="{{false}}" enhanced="true">
          <view class="scrollx ">
            <view class="scrollx_item z-flex-y-c  z-radius-20" wx:for="{{nearSkill}}" wx:key="id" data-info="{{item}}"
              bindtap="skillTap">
              <image class="scrollx_item_img z-padding-16" src="{{tool.cdn(item.image)}}" mode="aspectFill" lazy-load
                lazy-load-margin="0" />
              <view>{{item.name}}</view>
              <view class="scrollx_item_tips z-font-22 z-flex z-radius-4 z-padding-lr-8 z-margin-b-24 z-margin-t-8">
                {{item.skillCate}}
              </view>
            </view>
          </view>
        </scroll-view>

        <block wx:for="{{cateGoods}}">
          <view class="zxcpage" wx:if="{{item.goodsList.length > 0}}" wx:key="id">
            <view class="index_samebox">
              <view class="index_samebox_top z-margin-tb-30">
                <view class="z-font-26 z-font-w">{{item.name}}</view>
                <view class="z-font-24 text_999" bindtap="cateList" data-item="{{item}}">更多
                  <van-icon name="arrow" />
                </view>
              </view>
            </view>
            <view class="fourbox z-margin-lr-32">
              <block wx:for="{{item.goodsList}}" wx:key='id' wx:for-item="val">
                <service info="{{val}}" type="{{1}}" bind:serviceTap="serviceTap"></service>
              </block>
            </view>

          </view>
        </block>
      </view>
    </scroll-view>
  </view>
  <image src="../../static/service/center.png" class="center" bindtap="center"></image>
  <view class="center2-box">
    <image src="../../static/service/center-kf.png" class="center2"></image>
    <button type="primary" open-type="contact" class="contact-btn"></button>
  </view>


  <view class="popup-bg" wx:if="{{showPopup}}" bindtap="hidePopup"></view>
  <view class="popup" wx:if="{{showPopup}}">
    <!-- 图片 -->
    <view wx:if="{{config.home_image}}">
      <image bind:tap="toPage" class="home_image" src="{{tool.cdn(config.home_image)}}" mode="widthFix" />
      <image bind:tap="hidePopup" class="shut-img" src="../../static/index/shut-img.png" alt="" />
    </view>
    <!-- 公告 -->
    <view wx:elif="{{config.home_content}}" class="popNotice-box z-padding-lr-32 z-padding-t-32">
      <view class="notice-img-box">
        <image class="noticepop-img" src="../../static/index/notice-pop.png" mode="" />
      </view>
      <image class="notice-bac" src="../../static/index/notice-bac.png" mode="" />
      <view class="popNotice-title">公告</view>
      <view class="notice-content z-margin-t-32">{{config.home_content}}</view>
      <view class="notice-btn z-text-c z-padding-tb-24" bind:tap="hidePopup">关闭</view>
    </view>
  </view>
</view>