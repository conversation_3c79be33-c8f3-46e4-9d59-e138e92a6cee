// service/paypage/paypage.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    paytype:0,
    price: '',
    order_id: '',
    type: '',
    safeBottom: `30px`,
    add_ids: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const high = app.globalData.safeBottom
    this.setData({
      safeBottom: `${high}px`
    })
    this.setData({
      order_id: options.order_id,
      price: options.price,
      type: options.type,
      add_ids: options.add_ids
    })
  },
  repay(){
    wx.requestSubscribeMessage({
      tmplIds: [app.globalData.templateconfig.user_notice_template,app.globalData.templateconfig.user_order_template,app.globalData.templateconfig.order_finish_template],
      complete :(res) =>{ 
        this.pay()
      }
    })
  },
  pay(){
    let data = {
      paytype: this.data.paytype,
      id: this.data.order_id
    }
    let url;
    if(this.data.type == 3){
      data['price'] = this.data.price
      url = 'order/premiumorder'
    }else if(this.data.type == 2){
      data['add_ids'] = this.data.add_ids
      url = 'order/addorder'
    }else if(this.data.type == 1){
      url = 'order/pay'
    }
    http.get(url, data ,true, false).then(res => {
      if(this.data.paytype == 4){
        wx.redirectTo({
          url: '/service/result/result?type='+this.data.type
        })
      }else {
        wx.requestPayment({
          timeStamp: res.data.pay.timeStamp,
          nonceStr: res.data.pay.nonceStr,
          package: res.data.pay.package,
          signType: res.data.pay.signType,
          paySign: res.data.pay.paySign,
          success: (res) =>{ 
            wx.redirectTo({
              url: '/service/result/result?type='+this.data.type
            })
          },
          fail: (res) =>{

           }
        })
      }
    }).catch(e => {
      util.toast(e.data.msg)
    })
  },
  choosepayway(){
    this.setData({
      paytype:this.data.paytype===4?0:4
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})