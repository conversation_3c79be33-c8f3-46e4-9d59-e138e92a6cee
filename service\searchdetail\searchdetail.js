// pagesA/searchdetail/searchdetail.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    value:'',
    history: [],
    type: 1,
    list: [],
    page: 1,
    address: ''
  },
  serviceTap(e){
    util.skip('/service/servedetail/servedetail?id='+e.currentTarget.dataset.info.id)
  },
  getList(){
    http.get('goods/getlist', {
      page: this.data.page,
      is_rank:  1,
      city: this.data.address.city
    }).then(res => {
      this.setData({
        list: res.data
      })
    })
  },
  onLoadAddress(option){
    this.setData({
      address: app.globalData.address
    })
    this.getList()
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      type: options.type,
    })
  },
  del(){
    this.setData({
      history: []
    })
    wx.setStorageSync('keyword'+this.data.type,this.data.history)
  },
  wordTap(e){
    this.setData({
      value: e.currentTarget.dataset.info
    })
    this.onSearch()
  },
  onChange(e) {
    this.setData({
      value: e.detail,
    });
  },
  onSearch(){
    if(this.data.value === '')  return util.toast('请输入搜索内容')
    let index = this.data.history.indexOf(this.data.value)
    if(index !== -1){
      this.data.history.splice(index,1)
    }
    this.data.history.unshift(this.data.value)
    this.setData({
      history: this.data.history
    })
    wx.setStorageSync('keyword'+this.data.type,this.data.history)
    wx.navigateTo({
      url: '/service/searchresult/searchresult?value=' + this.data.value + '&type='+this.data.type,
    })
  },


  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.setData({
      history: wx.getStorageSync('keyword'+this.data.type) || []
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {}
  }
})