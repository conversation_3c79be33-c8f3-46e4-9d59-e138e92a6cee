/* service/myorder/myorder.wxss */


.myorder{
height: 100vh;
display: flex;
flex-direction: column;
}
.top-box{
  flex-shrink: 0;
}
.main-box{
  flex: 1;
  overflow: hidden;
}
.scroll-box{
  box-sizing: border-box;
  height: 100%;
}
.popbox{
  width: 686rpx;
  box-sizing: border-box;
}

.money{
  color: #FF9600;
}
.input_box{
  background-color: #F5F7F9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
input{
  text-align: right;
}
.popbox_bottom{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.popbox_bottom_money{
  display: flex;
  align-items: center;
}
.van-button{
  border-radius: 15rpx !important;
}
.plusbox{
  box-sizing: border-box;
}
.plus_item{
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #F5F7F9;
}
.plus_item_left{
  display: flex;
  flex-direction: column;
}

