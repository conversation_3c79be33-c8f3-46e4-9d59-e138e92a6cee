/* service/menber/menber.wxss */
.level{
  padding-bottom: 100rpx;
  overflow-y: auto;
  height: 100vh;
  box-sizing: border-box;
}
.level::-webkit-scrollbar{
  display: none;
}
.menber_top {
  width: 750rpx;
  background: linear-gradient(180deg, #C1EBC8 0%, #F0FEC8 100%);
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  opacity: 1;
  padding: 32rpx 32rpx 100rpx;
  box-sizing: border-box;
}

.menber_info {
  display: flex;
  align-items: center;
}

.menber_avator {
  width: 95rpx;
  height: 95rpx;
  border-radius: 50%;
}

.menber_info_right {
  display: flex;
  flex-direction: column;
  height: 95rpx;
  justify-content: space-around;
}

.menber_content {
  background-color: #fff;
  position: relative;
  box-sizing: border-box;
  margin-top: -50rpx;
  border-radius: 40rpx;
}

page {
  background-color: #fff;
}

.price_box {
  border: 10rpx solid #EDF1F4;
  width: 686rpx;
  margin: 30rpx auto;
  box-sizing: border-box;
  background-color: #F5F7F9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.active {
  border: 10rpx solid #1782FA;
  width: 686rpx;
  margin: 30rpx auto;
  box-sizing: border-box;
  background-color: #E0FCCC;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.yellow {
  color: var(--mainmoneycolor);
}
.yellowed{
  background-color: rgba(255, 150, 0, 0.30);
  color: var(--mainmoneycolor);
}

.line_through {
  text-decoration: line-through;

}

.realmoney {
  display: flex;
  align-items: center;
}

.price_box_left {
  display: flex;
  align-items: center;
  flex: 3;
}

.price_box_left_left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.bigsize {
  font-size: 50rpx;
}

.fakemoney {
  background-color: #EDF1F4;
}


.price_box_left_right {

  display: flex;
  flex-direction: column;
}

.price_box_right {
  background-color: #fff;
  flex: 1;
  text-align: center;
}

.newbtn {
  flex: 1;
  text-align: center;
  background: linear-gradient(270deg, #A8BBFF 0%, #1782FA 100%);
  color: #fff;
}

.read_box {
  display: flex;
  align-items: center;
}

.choose_img {
  width: 32rpx;
  height: 32rpx;
}

.namebox {
  display: flex;
  align-items: center;
}

.vip {
  width: 50rpx;
  height: 50rpx;
}
.namebox_text {
  display: flex;
  align-items: center;
}

.plusname{
  background: linear-gradient(90deg, #FFB40E 0%, #FF5942 100%);
  color: #fff;
  height: 46rpx;
  border-radius: 0 24rpx 24rpx 0;
  padding: 0 24rpx 0 40rpx;
  margin-left: -30rpx;
}
.vip{
  width: 50rpx;
  height: 50rpx;
  z-index: 10rpx;
  position: relative;
}