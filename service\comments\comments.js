// service/comments/comments.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list:[],
    finish: false,
    loading: false,
    page: 1,
    num: '',
    type: '',
    id: '',
    active: 0
  },
  change(e){
    this.setData({
      active: e.currentTarget.dataset.index
    })
    this.reload()
  },
  reload(){
    this.setData({
      list: [],
      page: 1,
      finish: false
    })
    this.getList()
  },
  more(){
    this.setData({
      page: ++this.data.page
    })
    this.getList()
  },
  copy(e){
    wx.setClipboardData({
      data: e.currentTarget.dataset.content,
      success (res) {
        util.toast('已复制')
      }
    })
  },
  getList(){
    if(this.data.finish){
      return 
    }
    if(this.data.loading){
      return 
    }
    this.setData({
      loading: true
    })
    let data = {
      page: this.data.page,
    }
    if(this.data.type == 'goods'){
      data['goods_id'] = this.data.id
    }else if(this.data.type == 'skill'){
      data['skill_id'] = this.data.id
    }else if(this.data.type == 'shop'){
      data['shop_id'] = this.data.id
    }else if(this.data.type == 'user'){
      data['user_id'] = this.data.id
    }
    if(this.data.active == 1){
      data['score'] = 5
    }else if(this.data.active == 2){
      data['images'] = 1
    }else if(this.data.active == 3){
      data['score'] = 2
    }else if(this.data.active == 4){
      data['score'] = 1
    }
    http.get('comment/getlist', data).then(res => {
      if(res.data.length === 0){
        this.setData({
          finish: true
        })
      }
      let arr = this.data.list.concat(res.data)
      this.setData({
        list: arr
      })
      
    }).finally(res => {
      this.setData({
        loading: false
      })
    })
  },
  getInfo(){
    let data = {
      type: this.data.type
    }
    if(this.data.type == 'goods'){
      data['goods_id'] = this.data.id
    }else if(this.data.type == 'skill'){
      data['skill_id'] = this.data.id
    }else if(this.data.type == 'shop'){
      data['shop_id'] = this.data.id
    }else if(this.data.type == 'user'){
      data['user_id'] = this.data.id
    }
    http.get('comment/totalcomment', data).then(res => {
      this.setData({
        num: res.data
      })
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      type: options.type,
      id: options.id
    })
    this.getInfo()
    this.reload()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    

  }
})