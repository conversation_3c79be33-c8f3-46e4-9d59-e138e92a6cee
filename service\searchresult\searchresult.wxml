<!--pagesA/searchresult/searchresult.wxml-->
<view class="searchresult">
  <view class="container_top">
    <view class="search">
      <van-search value="{{ value }}" shape="round" background="#fff" placeholder="请输入搜索关键词" use-right-icon-slot
        bind:change="onChange" bind:search="onSearch">
        <view class="right-icon z-font-22 z-flex" slot="right-icon" bind:tap="onSearch">搜索</view>
      </van-search>
    </view>
    <view class="z-flex-c z-padding-24">
      <view  class="screen z-flex z-flex-1 {{ is_price === '' && is_rank === '' ? 'active' : ''}}" data-index="1" bindtap="rankTap">
        <view class="screen-word z-font-24">综合排序</view>
      </view>
      <view class="screen z-flex z-flex-1 {{ is_rank=== 1 ? 'active' : ''}}" data-index="2" bindtap="rankTap">
        <view class="screen-word z-font-24">销量</view>
      </view>
      <view class="screen z-flex z-flex-1 {{ is_price !== '' ? 'active' : ''}}" data-index="3" bindtap="rankTap">
        <view class="screen-word z-font-24 z-margin-r-8">价格</view>
        <image src="../../static/service/norank.png" class="rank-icon" wx:if="{{is_price === ''}}" mode="widthFix"></image>
        <image src="../../static/service/down.png" class="rank-icon" wx:elif="{{is_price === 1}}" mode="widthFix"></image>
        <image src="../../static/service/top.png" class="rank-icon" wx:elif="{{is_price === 2}}" mode="widthFix"></image>
      </view>
      <view class="z-flex z-flex-1">
        <image src="/static/index/row.png" class="icons" wx:if="{{shape == 2}}" bindtap="listChange" data-index="1"></image>
        <image src="/static/index/column.png" class="icons" wx:else  bindtap="listChange" data-index="2"></image>
      </view>

    </view>
  </view>
  <view class="main-box">
    <scroll-view scroll-y="true" class="scroll z-padding-32" bindscrolltolower="lowerthreshold">
      <view class="{{shape == 1 ? 'service-box' : '' }}">
        <block wx:for="{{list}}">
          <service info="{{item}}" type="{{shape}}" bind:serviceTap="serviceTap"></service>
        </block>
      </view>
      <van-empty description="暂无项目" wx:if="{{finish && list.length === 0}}" />
    </scroll-view>
  </view>
</view>