<!--component/coupon/coupon.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="coupons_box z-padding-32 z-radius-24 z-margin-b-32"  wx:key="*this">
  <view class="coupons_box_top">
    <view class="coupons_box_top_left">
      <view>{{info.reduce}}元代金券</view>
      <view class="text_999 z-font-24 z-margin-t-24" wx:if="{{type == 2}}">{{tool.formatTime(info.exittime)}}到期</view>
      <view class="text_999 z-font-24 z-margin-t-24" wx:else>领取后{{info.effective_day}}天内有效</view>
    </view>
    <view class="coupons_box_top_left">
      <view class="money z-font-w">
        <view>￥</view><view class="money_text">{{info.reduce}}</view>
      </view>
      <view class="text_999 z-font-24 z-margin-t-8">满{{info.achieve}}可用</view>
    </view>
  </view>
  <van-divider />
  <view class="coupons_box_top">
    <view class="green z-font-28" wx:if="{{info.goods_id > 0}}">{{info.shopName||''}}{{info.goodsName}}专用券</view>
    <view class="green z-font-28" wx:else>{{info.shopName||''}}通用券</view>
    <view class="coupon-btn z-flex z-font-24 z-radius-4 use" wx:if="{{goods && info.shop_id && (info.type === 3) && goods.shop_id == info.shop_id && goods.goods_id == info.goods_id && goods.money >= info.achieve}}" bindtap="couponTap">立即使用</view>
    <view class="coupon-btn z-flex z-font-24 z-radius-4 use" wx:elif="{{goods && info.shop_id && (info.type === 2) && goods.shop_id == info.shop_id && goods.money >= info.achieve}}" bindtap="couponTap">立即使用</view>
    <view class="coupon-btn z-flex z-font-24 z-radius-4 use" wx:elif="{{goods && !info.shop_id && (info.type === 0) && goods.money >= info.achieve}}" bindtap="couponTap">立即使用</view>
    <view class="coupon-btn z-flex z-font-24 z-radius-4 use" wx:elif="{{goods && !info.shop_id && (info.type === 1) && goods.goods_id == info.goods_id && goods.money >= info.achieve}}" bindtap="couponTap">立即使用</view>
    <view class="z-flex z-font-24 z-radius-4 text_999" wx:elif="{{goods}}">不可使用</view>
    <view class="coupon-btn z-flex z-font-24 z-radius-4 use" wx:elif="{{info.userState == 1 }}" data-type="2" bindtap="couponTap">已领取</view>
    <view class="coupon-btn z-flex z-font-24 z-radius-4 use" wx:elif="{{info.state === 0 }}" data-type="2" bindtap="couponTap">立即使用</view>
    <view class="z-flex z-font-24 z-radius-4 text_999" wx:elif="{{info.state === 1 }}">已使用</view>
    <view class="z-flex z-font-24 z-radius-4 text_999" wx:elif="{{info.state === -1 }}">已过期</view>
    <view class="coupon-btn z-flex z-font-24 z-radius-4" wx:else data-type="1" bindtap="couponTap">立即领取</view>
  </view>
</view>
