/* service/applyrefund/refund.wxss */
page{
  background-color: #fff;
}
.samebox{
  width: 686rpx;
  box-sizing: border-box;
  margin: 0 auto;
  background-color: #F5F7F9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.refund_price{
  flex: 1;
  margin-left: 20rpx;
}
.green{
  color: var(--maingreencolor);
}
input{
  text-align: right;
}
.smcontent{
  width: 686rpx;
  background-color: #eff4f8;
  box-sizing: border-box;
}

.icon{
  width: 150rpx;
  height: 150rpx;
  margin: 0 24rpx 24rpx 0;
}

.state {

  width: 750rpx;
  height: 86rpx;
  background: #F0FAF3;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #1782FA;
  display: flex;
  align-items: center;
}

.refund{
  background: #FFEEEE;
  color: #FF2121;
}

.icons {
  width: 24rpx;
  height: 24rpx;
}


.images-box {
  position: relative;
}

.each-img-min {
  width: 150rpx;
  height: 150rpx;
  background: #F5F7F9;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
}

.image-box {
  flex-wrap: wrap;
}

.photo {
  height: 100%;
  width: 100%;
}

.del {
  position: absolute;
  width: 26rpx;
  height: 26rpx;
  right: 0;
  top: -13rpx;
  z-index: 10;
}


.notice{
  white-space: pre-wrap;
}