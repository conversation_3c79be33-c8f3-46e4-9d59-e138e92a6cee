/* service/report/report.wxss */
page{
  background-color: #fff;
  position: relative;
}
.samebox{
  width: 686rpx;
  box-sizing: border-box;
  margin: 0 auto;
  background-color: #F5F7F9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.samebox_left{
  display: flex;
  flex-direction: column;
}
.choose_img{
  width: 30rpx;
  height: 30rpx;
}
.smcontent{
  width: 686rpx;
  background-color: #eff4f8;
  box-sizing: border-box;
}
.block{
  width: 100vw;
  height: 200rpx;
}

.icon{
  width: 150rpx;
  height: 150rpx;
  margin: 0 24rpx 24rpx 0;
}


.state {

  width: 750rpx;
  height: 86rpx;
  background: #F0FAF3;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #1782FA;
  display: flex;
  align-items: center;
}

.icons {
  width: 24rpx;
  height: 24rpx;
}



.images-box {
  position: relative;
}

.each-img-min {
  width: 150rpx;
  height: 150rpx;
  background: #F5F7F9;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
}

.image-box {
  flex-wrap: wrap;
}

.photo {
  height: 100%;
  width: 100%;
}

.del {
  position: absolute;
  width: 26rpx;
  height: 26rpx;
  right: 0;
  top: -13rpx;
  z-index: 10;
}