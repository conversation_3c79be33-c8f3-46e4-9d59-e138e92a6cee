<!--service/menber/menber.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="level" style="{{safeBottom}}">
  <view class="menber_top">
    <view class="menber_info">
      <image src="{{tool.cdn(info.avatar)}}" class="menber_avator z-margin-r-24" mode="" />
      <view class="menber_info_right">
        <view class="namebox">
          <view class="z-margin-r-32">{{info.nickname}}</view>


          <view class="namebox_text  z-font-22 " wx:if="{{info.plusInfo.is_plus > 0}}">
            <image src="../../static/index/vip.png" class="vip" mode="" />
            <view class="plusname z-flex">{{info.plusInfo.plusname}}</view>
          </view>
        </view>
        <view class="text_999 z-font-24 z-margin-t-16" wx:if="{{info && info.plusInfo.is_plus > 0}}">
          有效期至{{tool.formatTime(info.plusInfo.plustime)}}</view>
        <view class="text_999 z-font-24 z-margin-t-16" wx:else>开通会员，即可享受订单折扣</view>
      </view>
    </view>
  </view>
  <view class="menber_content z-padding-32 ">
    <view class="{{plus_id===item.id?'active z-radius-32 z-padding-32':'price_box z-radius-32 z-padding-32'}}"
      bind:tap="chose" data-id="{{item.id}}" wx:for="{{plus}}" wx:key="id">
      <view class="price_box_left">
        <view class="price_box_left_left z-margin-r-16">
          <view class="realmoney yellow">
            <view class="z-margin-t-8">￥</view>
            <view class="z-font-w bigsize">{{item.price}}</view>
          </view>
          <view
            class="z-font-24 z-pading-tb-8 z-margin-t-8 z-padding-lr-8 z-radius-8 line_through {{plus_id===item.id? 'yellowed  ':'fakemoney text_999 '}}">
            ￥{{item.original_price}}
          </view>
        </view>
        <view class="price_box_left_right">
          <view class="z-font-30">{{item.name}}</view>
          <view class="z-font-20 text_999 z-margin-t-16 ">开通会员下订单可享受{{item.discount/10}}折优惠</view>
        </view>
      </view>
      <view
        class="z-radius-32 z-padding-8 z-font-w z-font-26 z-margin-l-24 {{plus_id==item.id?'newbtn ':'price_box_right'}}"
        data-id="{{item.id}}" catchtap="pay">
        立即开通
      </view>
    </view>

    <view class="read_box" bind:tap="readclick">
      <image src="../../static/login/choose.png" wx:if="{{agree==0}}" class="choose_img" mode="" />
      <image src="../../static/login/choosed.png" wx:else class="choose_img" mode="" />
      <view class="z-font-24 z-margin-l-16">开通前请阅读 <text class="green" catchtap="infoTap" data-info="plus_info_id"
          data-name="充值会员须知">《充值会员须知》</text> </view>
    </view>


  </view>
</view>