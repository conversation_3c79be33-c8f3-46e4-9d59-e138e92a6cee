// service/applyrefund/refund.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    status: '',
    columns: [],
    reason: '',
    fileList:[],
    show: false,
    refund_reason_id: '',
    id: '',
    refund_price: '',
    images: [],
    payprice: '',
    state: '',
    note: '',
    config: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      id: options.order_id
    })
    this.getInfo()
  },
  // 导航栏返回处理
  onClickLeft(){
    util.back()
  },
  onLoadConfig(option){
    this.setData({
      config: app.globalData.config
    })
  },
  selectReason(){
    this.setData({
      show: true
    })
  },
  close(){
    this.setData({
      show: false
    })
  },
  chooseImage(e){
    let index = e.currentTarget.dataset.index
    http.chooseImg(['album', 'camera'], true, true, this.data.token).then(res => {
      let arr = this.data.images;
      if(index !== undefined){
        arr.splice(index,1,res.data.url)
      }else {
        arr.push(res.data.url)
      }
      this.setData({
        images: arr
      })
    console.log(this.data.images,'................')

    })
  },
  setprice(e){
    this.setData({
      refund_price: e.detail.value
    })
  },
  setContent(e){
    this.setData({
      content: e.detail.value
    })
  },
  confirm(e){
    this.setData({
      refund_reason_id: e.detail.value.id,
      reason: e.detail.value.name,
      show: false
    })
  },
  delImages(e){
    let index = e.currentTarget.dataset.index
    this.data.images.splice(index,1)
    this.setData({
      images: this.data.images
    })
  },
  cancel(){
    http.get('order/cancelRefund', {
      order_id: this.data.id
    }).then(res => {
      util.toast(res.msg)
      const eventChannel = this.getOpenerEventChannel()
      eventChannel.emit('applyRefund');
      setTimeout(()=>{
        util.back();
      }, 1000)
   
      
    })
  },
  resave(){
    if (this.data.refund_reason_id === '') return util.toast('请选择退款原因')
    if (this.data.refund_price === '') return util.toast('请输入退款金额')
    if(Number(this.data.refund_price) > Number(this.data.payprice)) return util.toast('退款金额不能大于'+this.data.payprice+'元')
    wx.requestSubscribeMessage({
      tmplIds: [app.globalData.templateconfig.user_sales_template],
      complete :(res) =>{ 
        this.save()
      }
    })
  },
  save(){
    http.post('order/refundorder',{
      type:'refund',
      id:  this.data.id,
      refund_reason_id:  this.data.refund_reason_id,
      refund_price:  this.data.refund_price,
      images: this.data.images.join(','),
      content: this.data.content
    },true).then(res => {
      util.toast(res.msg)
      setTimeout(()=>{
        util.back();
      }, 1000)
    })
  },
  getInfo(){
     
    http.get('order/orderInfo', {
      id: this.data.id
    }).then(res => {
      this.setData({
        info: res.data,
        payprice: res.data.payprice
      })
   
      
    })
    http.get('order/refundreason').then(res => {
      
      this.setData({
        columns: res.data
      })
      http.post('order/refundInfo',{
        order_id: this.data.id
      }).then(res => {
        if(res.data){
          this.setData({
            state: res.data.state,
            reason: res.data.refund_reason,
            refund_price: res.data.refund_price,
            content: res.data.content,
            images: res.data.images === '' ? [] : res.data.images,
            note: res.data.note
          })
          for(let i=0; i < this.data.columns.length; i++){
            if(this.data.columns[i].name == this.data.reason){
              this.setData({
                refund_reason_id: this.data.columns[i].id
              })
            }
          }
        }
        
      })
      
    })
  },
  
  afterRead(event) {
    const { file } = event.detail;
    // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
    wx.uploadFile({
      url: 'https://example.weixin.qq.com/upload', // 仅为示例，非真实的接口地址
      filePath: file.url,
      name: 'file',
      formData: { user: 'test' },
      success(res) {
        // 上传完成需要更新 fileList
        const { fileList = [] } = this.data;
        fileList.push({ ...file, url: res.data });
        this.setData({ fileList });
      },
    });
  },
  bindPickerChange: function(e) {
    this.setData({
      index: e.detail.value
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})