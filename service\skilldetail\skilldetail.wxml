<!--service/skilldetail/skilldetail.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="detail" wx:if="{{info}}">
  <view>
    <swiper class="swiper_box" bindchange="bindchange">
      <swiper-item wx:for="{{info.images}}" wx:key="*this">
        <image src="{{tool.cdn(item)}}" class="swiper_img" mode="aspectFill"   bindtap="previewImages" data-index="{{index}}" />
      </swiper-item>
    </swiper>
    <view class="tips z-padding-tb-8 z-padding-lr-16 z-radius-32 z-font-24">
      {{current+1}}/{{info.images.length}}</view>
  </view>
  <view class="skillinfo z-margin-t-24 z-radius-16">
    <view class="skillinfo_top  z-padding-32">
      <view style="display: flex;">
        <view class="z-font-w z-font-32 z-margin-r-24">{{info.name}}</view>
        <view class="time z-font-24 ">
          <view class="time_one z-padding-tb-8 z-padding-lr-16 z-radius-tl-8  z-radius-bl-8">最早可约</view>
          <view class="time_two z-padding-tb-8 z-padding-lr-16  z-radius-tr-8  z-radius-br-8">{{tool.formatTime(info.skillTime,'mm-dd HH:MM')}}</view>
        </view>
      </view>
      <view class="followed z-flex z-radius-4 z-font-22" wx:if="{{info.followState}}" bindtap="collect">已关注</view>
      <view class="follow z-flex z-radius-4 z-font-22" wx:else bindtap="collect">关注</view>

    </view>
    <view class="skillinfo_tips z-padding-b-32 z-padding-lr-32">
      <view class="skillinfo_tips_itme z-radius-8 z-font-24 z-flex  z-padding-lr-16 text_999">
        {{info.sex === 1 ? '男' : '女'}}
      </view>
      <view class="skillinfo_tips_itme z-radius-8 z-font-24 z-flex  z-padding-lr-16 text_999">
        {{info.nation}}
      </view>
      <view class="skillinfo_tips_itme z-radius-8 z-font-24 z-flex z-padding-lr-16 text_999">
        {{info.edu}}
      </view>
      <view class="skillinfo_tips_itme z-radius-8 z-font-24 z-flex  z-padding-lr-16 text_999">
        {{info.age}}岁
      </view>
      <view class="z-flex-1"></view>
      <view class="z-font-24 text_999" wx:if="{{!info.shopname}}">{{info.distance}}km</view>
    </view>
    <view class="shopbox z-padding-b-32 z-padding-lr-32" wx:if="{{info.shopname}}">
      <view class="shopname z-radius-8 z-padding-lr-8 " bindtap="shopTap">
        <image src="../../static/index/shop.png" class="shopimg" mode="" />
        <view class="z-font-22 z-margin-lr-8">{{info.shopname}}</view>
        <van-icon size='10' name="arrow" />
      </view>
      <view class="z-flex-1"></view>
      <view class="z-font-24 text_999">{{info.distance}}km</view>
    </view>
    <view class="skillinfo_btoom z-padding-32 " bindtap="skillAuth">
      <view class="skillinfo_btoom_item">
        <image src="../../static/index/smrz.png" class="skillinfo_btoom_img" mode="" />
        <view class="z-font-22 z-margin-l-8">实名认证</view>
      </view>
      <view class="skillinfo_btoom_item">
        <image src="../../static/index/zgz.png" class="skillinfo_btoom_img" mode="" />
        <view class="z-font-22 z-margin-l-8">从业资格证</view>
      </view>
      <view class="skillinfo_btoom_item">
        <image src="../../static/index/jkz.png" class="skillinfo_btoom_img" mode="" />
        <view class="z-font-22 z-margin-l-8">健康证</view>
      </view>
      <view class="skillinfo_btoom_item text_999 z-font-22">
        <view class="z-font-22 z-margin-l-8">查看</view>
        <van-icon name="arrow" />
      </view>
    </view>
  </view>
  <view class="z-font-w z-margin-lr-32 z-margin-t-40 z-margin-b-24">服务项目</view>
  <view class="scroll z-padding-lr-32">
    <block wx:for="{{info.goodsList}}" wx:for-item="val">
      <service info="{{val}}" type="{{2}}" bind:serviceTap="serviceTap"></service>
    </block>
  </view>
  <view class="comment_box" wx:if="{{num.totalNum>0}}">
        <view class="comment_box_top z-margin-t-32 z-margin-b-24">
          <view class="z-font-w">评价({{num.totalNum}})</view>
          <view class="text_999 z-font-24" bindtap="comment">好评率{{num.goodPercent*100}}%
            <van-icon name="arrow" />
          </view>
        </view>
        <block wx:for="{{list}}" wx:key="item.id" wx:if="{{index < 2}}">
          <comment info="{{item}}"></comment>
        </block>
  </view>
</view>