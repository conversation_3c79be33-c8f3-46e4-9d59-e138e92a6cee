/* component/pack.wxss */
@import '../../common.wxss';
.item-each {
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.sheng-tag {
  display: inline-block;
  /* width: 101rpx;
height: 34rpx; */
  padding: 3rpx 11rpx;
  background: linear-gradient(180deg, #FFE8B2 0%, #F8EBD3 100%);
  border-radius: 20rpx 0rpx 20rpx 0rpx;
  font-weight: 500;
  font-size: 18rpx;
  color: #814F1E;
  position: absolute;
  top: 0;
  left: 0;
}

.item-each-img-box {
  width: 157rpx;
  height: 157rpx;
  background: #DCCDEB;
  border-radius: 10rpx 10rpx 10rpx 10rpx;
}

.setMeal-l-text {
  font-weight: 800;
  font-size: 30rpx;
  color: #20452B;
}

.setMeal-r-text {
  font-weight: 500;
  font-size: 22rpx;
  color: #1782FA;
}

.setMeal-label {
  background: linear-gradient(90deg, #92E5AB 0%, #1782FA 100%);
  border-radius: 16rpx 16rpx 16rpx 2rpx;
  font-weight: 500;
  font-size: 22rpx;
  color: #FFFFFF;
  padding: 2rpx 10rpx;
}

.setMeal-item-name {
  font-weight: 800;
  font-size: 30rpx;
  color: #1C274C;
}

.setMeal-item-gg {
  font-weight: 500;
  font-size: 22rpx;
  color: #A4A9B7;
  margin-top: 8rpx;
}

.price_box_left {
  display: flex;
  align-items: center;
  color: var(--mainmoneycolor);
}