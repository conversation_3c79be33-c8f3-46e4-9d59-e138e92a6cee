/**index.wxss**/
@import "../../common.wxss";

.index_box {
  box-sizing: border-box;

  background: linear-gradient(180deg, #62CB81 0%, #E3FDCE 56%, rgba(227, 253, 206, 0) 100%) no-repeat;
  background-size: 750rpx 500rpx;
  background-color: #EDF1F4;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.index_top {

  width: 100vw;
  box-sizing: border-box;
}

.index_top_location {
  display: flex;
  align-items: center;
  width: 520rpx;
}

.index_top_location_titile {
  width: 158rpx;
  height: 37rpx;
}

.location_img {
  width: 24rpx;
  height: 24rpx;
}

.location_text {
  color: #fff;
}

.more {
  width: 24rpx;
  height: 24rpx;
}

.index_bottom {
  flex: 1;
  overflow: hidden;
}

.index_main {
  height: 100%;
}

.index_content {
  background-color: #EDF1F4;
  margin-top: 20rpx;
}

.search_box {
  display: flex;
  align-items: center;
  background: #fff;
  width: 686rpx;
  height: 80rpx;
  box-sizing: border-box;
}

.search_img {
  width: 24rpx;
  height: 24rpx;
}

.input_box {
  width: 90%;
  color: #999;
}

.swiper_box {
  width: 686rpx;
  margin: 0 auto;
  height: 300rpx;
  border-radius: 20rpx;
  margin-top: 20rpx;
  overflow: hidden;
}

.swiper_img {
  width: 100%;
  height: 100%;
}

.notice-box {
  width: 638rpx;
  margin: 20rpx auto 0;
  background-color: #FFF;
}

.notice-img {
  width: 32rpx;
  height: 32rpx;
  vertical-align: middle;
}

.ten_btn {
  width: 686rpx;
  margin: 0 auto;
  background: #FFFFFF linear-gradient(90deg, #F0FFF3 0%, #EDFDDE 100%);
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
}

.ten_btn_box {
  background-color: #fff;
  width: 686rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.ten_btn_item {
  width: 20%;
}

.ten_btn_img {
  width: 72rpx;
  height: 72rpx;
}

.ten_btn_bottom {
  width: 686rpx;
  margin: 0 auto;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}

.ten_btn_bottom_item {
  display: flex;
  align-items: center;
}

.ten_btn_bottom_img {
  width: 20rpx;
  height: 20rpx;
}

.index_samebox {
  width: 686rpx;
  margin: 0 auto;
}

.index_samebox_top {
  display: flex;
  justify-content: space-between;
}

.scrollx {
  display: flex;
  padding: 0 20rpx;
}

.scrollx_item {
  background-color: #fff;
  box-sizing: border-box;
  margin: 0 12rpx;
}

.scrollx_item_img {
  width: 181rpx;
  height: 181rpx;
}

.scrollx_item_tips {
  background: #F0FAF3;
  height: 38rpx;
  color: #62CB81;
}

.zxcpage {
  background-color: #EDF1F4;
  width: 100vw;
}

.fourbox {
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
  justify-content: space-between;
}



.center {
  position: fixed;
  left: 10rpx;
  bottom: 400rpx;
  width: 66rpx;
  height: 74rpx;
}

.center2-box {
  position: fixed;
  right: 10rpx;
  bottom: 200rpx;
  width: 66rpx;
  height: 74rpx;
}

.center2 {
  width: 66rpx;
  height: 74rpx;
}

.contact-btn {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}



.popNotice-box {
  width: 536rpx;
  height: 648rpx;
  /* background: linear-gradient( 180deg, #E3FFE0 0%, #FFFFFF 100%); */
  background: linear-gradient(#E5FFE0, #FFF, #FFF, #FFF);
  border-radius: 60rpx 60rpx 60rpx 60rpx;
}

.popNotice-title {
  font-size: 50rpx;
  color: #1C274C;
  line-height: 59rpx;
  text-align: center;
  position: relative;
  top: -60rpx;
}

.notice-content {
  width: 460rpx;
  height: 300rpx;
  overflow: hidden;
  overflow-y: scroll;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 30rpx;
  color: #1C274C;
  margin: auto;
  line-height: 50rpx;
  position: relative;
  top: -60rpx;
}

.notice-img-box {
  width: 145rpx;
  height: 145rpx;
  margin: auto;
  position: relative;
  top: -90rpx;
  z-index: 10;
}

.noticepop-img {
  width: 145rpx;
  height: 145rpx;
  z-index: 11;
}

.notice-btn {
  width: 456rpx;
  background: #EDEEF1;
  border-radius: 21rpx 21rpx 21rpx 21rpx;
  margin: auto;
  font-weight: bold;
  font-size: 30rpx;
  color: #A4A9B7;
  /* margin-top: 56rpx; */
}

.notice-bac {
  width: 269rpx;
  height: 260rpx;
  position: absolute;
  top: 0;
  right: 35rpx;
}

.home_image {
  width: 520rpx;
}

.shut-img {
  width: 52rpx;
  height: 52rpx;
  display: block;
  margin: auto;
  margin-top: 32rpx;
}




/* 套餐 */
.setMeal-box {
  width: 638rpx;
  /* height: 516rpx; */
  background: linear-gradient(90deg, #C9F1D5 0%, #CCF3D8 100%);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 2rpx solid;
  border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 2 2;
  margin: auto;
  margin-top: 24rpx;
  position: relative;
}

.setMeal-1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 132rpx;
  height: 72rpx;
}

.setMeal-2 {
  position: absolute;
  top: 0;
  right: 0;
  width: 273rpx;
  height: 194rpx;
}

.setMeal-r-text{
  position: relative;
  z-index: 10;
}





.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.popup-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* background-color: #fff; */
  /* padding: 20rpx; */
  border-radius: 10rpx;
}