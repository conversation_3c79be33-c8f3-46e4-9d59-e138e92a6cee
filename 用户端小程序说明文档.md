# 用户端小程序说明文档

## 项目概述
用户端小程序是一个服务预订平台，用户可以浏览服务、下单、支付、管理地址等。使用Vant UI组件库构建界面。

## 技术栈
- 微信小程序原生框架
- Vant Weapp UI组件库
- API基础路径：`/api/service/`

## 页面结构

### 主要页面（TabBar）
1. **首页** (`pages/index/index`)
   - 功能：展示服务分类、推荐服务、搜索入口
   - 主要接口：首页数据获取

2. **分类** (`pages/classify/classify`)
   - 功能：服务分类浏览
   - 主要接口：分类数据获取

3. **服务者** (`pages/servers/servers`)
   - 功能：浏览服务提供者列表
   - 主要接口：服务者列表获取

4. **商家** (`pages/business/business`)
   - 功能：浏览商家列表
   - 主要接口：商家列表获取

5. **我的** (`pages/my/my`)
   - 功能：个人中心、订单管理、设置
   - 主要接口：`用户信息获取`

### 子包页面（service目录）

#### 搜索相关
- `searchdetail/searchdetail` - 搜索详情页
- `searchresult/searchresult` - 搜索结果页

#### 服务相关
- `servedetail/servedetail` - 服务详情页
- `skilldetail/skilldetail` - 技能详情页
- `servelist/servelist` - 服务列表页

#### 订单相关
- `makeorder/makeorder` - 下单页面
- `myorder/myorder` - 我的订单
- `orderdetail/orderdetail` - 订单详情
- `applyrefund/applyrefund` - 申请退款
- `gocomment/gocomment` - 去评价
- `ordercomments/ordercomments` - 订单评价查看
- `paypage/paypage` - 支付页面

#### 地址管理
- `addressmanager/addressmanager` - 地址管理
- `addnewaddress/addnewaddress` - 添加新地址
- `selectAddress/selectAddress` - 选择地址
- `selectCity/selectCity` - 选择城市

#### 套餐相关
- `setMealDetail/setMealDetail` - 套餐详情
- `setMealBuy/setMealBuy` - 购买套餐
- `mysetMeal/mysetMeal` - 我的套餐
- `selectsetMeal/selectsetMeal` - 选择套餐
- `mealorder/mealorder` - 套餐订单
- `meallist/meallist` - 套餐列表

#### 用户相关
- `login/login` - 登录页面
- `myinfo/myinfo` - 个人信息
- `level/level` - 用户等级

#### 其他功能
- `couponscenter/couponscenter` - 优惠券中心
- `mycoup/mycoup` - 我的优惠券
- `code/code` - 二维码相关
- `feedback/feedback` - 意见反馈
- `question/question` - 常见问题
- `setpage/setpage` - 设置页面
- `about/about` - 关于我们
- `comments/comments` - 评价页面
- `shopdetail/shopdetail` - 商家详情
- `aptitude/aptitude` - 资质页面
- `report/report` - 举报页面
- `focus/focus` - 关注页面
- `info/info` - 信息页面
- `result/result` - 结果页面
- `balanceLog/balanceLog` - 余额记录
- `recharge/recharge` - 充值页面
- `notice/notice` - 通知页面
- `noticeDetail/noticeDetail` - 通知详情
- `skillauth/skillauth` - 技能认证
- `news/news` - 新闻页面

## 主要API接口

### 用户认证
- `POST user/userlogin` - 用户登录
  - 参数：`code`, `type: 0`, `encryptedData`, `iv`, `leader_id`
  - 返回：用户token和基本信息

- `POST index/updateuserinfo` - 更新用户信息
  - 参数：`type: 'update'`, `avatar`, `nickname`

### 订单相关
- `POST comment/getInfo` - 获取评价信息
  - 参数：`order_id`

### 套餐相关
- `POST package/createServiceOrder` - 创建套餐服务订单
  - 参数：`package_order_detail_id`, `num`, `memo`, `address_id`, `city`, `starttime`

### 文件上传
- `POST /api/common/upload` - 文件上传接口
  - 参数：`file`, `type`, `token`

## 组件使用
项目使用了完整的Vant Weapp组件库，包括：
- van-button, van-toast, van-icon
- van-divider, van-tab, van-tabs
- van-image, van-cell, van-cell-group
- van-uploader, van-rate, van-popup
- van-stepper, van-steps, van-transition
- van-picker

## 权限配置
- 获取用户位置信息：`getLocation`, `chooseLocation`
- 位置权限说明：用于小程序位置接口的效果展示

## 特色功能
1. **多端跳转**：支持跳转到商家端和接单端小程序
2. **地址管理**：完整的地址选择和管理功能
3. **套餐系统**：支持套餐购买和使用
4. **评价系统**：完整的订单评价功能
5. **优惠券系统**：优惠券领取和使用
6. **搜索功能**：支持服务和商家搜索
