/* service/myinfo/myinfo.wxss */
page{
  background-color: #fff;
}
.sameitem{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 120rpx; /* 设置整体行高 */
}

/* 左侧标签样式 */
.sameitem > view:first-child {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 28rpx;
  color: #323233;
  line-height: 40rpx;
  text-align: left;
  font-style: normal;
}

/* 右侧内容区域样式 */
.sameitem > view:last-child {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  text-align: center;
  font-style: normal;
}

.upimg{
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%; /* 设置头像为圆形 */
}
input{
  text-align: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  font-style: normal;
  border: none;
  outline: none;
  background: transparent;
}
.apply{
  background-color: var(--maingreencolor);
  width: 686rpx;
  left: 5%;
  position: absolute;
  bottom: 100rpx;
  box-sizing: border-box;
  color: #fff;
  text-align: center;
}

/* 导航栏样式 */
.van-nav-bar {
  background-color: #c8e2ff !important;
  z-index: 999;
}

.van-nav-bar__title {
  font-family: PingFangSC, PingFang SC !important;
  font-weight: 600 !important;
  font-size: 34rpx !important;
  color: #333333 !important;
  text-align: center !important;
  font-style: normal !important;
}

.van-nav-bar__arrow {
    color: #333333 !important;
    font-size: 44rpx !important;
    vertical-align: middle;
}

.van-nav-bar .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

.van-nav-bar__left .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}
.van-divider {
    margin: 0 0 0 0 !important;
}

input {
    text-align: right!important;
    font-family: PingFangSC, PingFang SC;
    margin-right: 10rpx!important;
}