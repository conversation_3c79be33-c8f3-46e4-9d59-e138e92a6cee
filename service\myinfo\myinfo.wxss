/* service/myinfo/myinfo.wxss */
page{
  background-color: #fff;
}
.sameitem{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.upimg{
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%; /* 设置头像为圆形 */
}
input{
  text-align: right;
}
.apply{
  background-color: var(--maingreencolor);
  width: 686rpx;
  left: 5%;
  position: absolute;
  bottom: 100rpx;
  box-sizing: border-box;
  color: #fff;
  text-align: center;
}

/* 导航栏样式 */
.van-nav-bar {
  background-color: #c8e2ff !important;
  z-index: 999;
}

.van-nav-bar__title {
  font-family: PingFangSC, PingFang SC !important;
  font-weight: 600 !important;
  font-size: 34rpx !important;
  color: #333333 !important;
  text-align: center !important;
  font-style: normal !important;
}

.van-nav-bar__arrow {
    color: #333333 !important;
    font-size: 44rpx !important;
    vertical-align: middle;
}

.van-nav-bar .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

.van-nav-bar__left .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}
