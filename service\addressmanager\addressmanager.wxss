/* service/addressmanager/addressmanager.wxss */
.address{
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-box{
  flex: 1;
  overflow: hidden;
}

.scroll-box{
  height: 100%;
}

.addressbox{
  width: 686rpx;
  background-color: #fff;
  margin: 0 auto;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.left_box{
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}
.choosed_img{
  width: 32rpx;
  height: 32rpx;
}
.left{
width: 32rpx;
height: 32rpx;
border-radius: 50%;
border: 1rpx solid #999;
}
.middle{
display: flex;
flex-direction: column;
overflow: hidden;
}
.right{
width: 40rpx;
height: 40rpx;
}
.addressdetail{
  display: flex;
  align-items: center;
}
.moren{
  font-weight: 400;
  color: var(--maingreencolor);
  border: 1rpx solid  var(--maingreencolor);
}
.addressbox_bottom{
  background-color: #fff;
  width: 100vw;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.btnbox{
  width: 686rpx;
}
.block{
  padding: 100rpx 0;
  width: 100vw;
  height: 20rpx;
}

/* 导航栏样式 */
.van-nav-bar {
  background-color: #c8e2ff !important;
  z-index: 999;
}

.van-nav-bar__title {
  font-family: PingFangSC, PingFang SC !important;
  font-weight: 600 !important;
  font-size: 34rpx !important;
  color: #333333 !important;
  text-align: center !important;
  font-style: normal !important;
}

.van-nav-bar__arrow {
    color: #333333 !important;
    font-size: 44rpx !important;
    vertical-align: middle;
}

.van-nav-bar .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

.van-nav-bar__left .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}
