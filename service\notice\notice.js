// service/notice/notice.js
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list:[],
    finish: false,
    loading: false,
    page: 1,
  },
  detail(e){
    util.skip('/service/noticeDetail/noticeDetail?id='+e.currentTarget.dataset.id)
  },
  reload(){
    this.setData({
      list: [],
      page: 1,
      finish: false
    })
    this.getList()
  },
  more(){
    this.setData({
      page: ++this.data.page
    })
    this.getList()
  },
  getList(){
    if(!wx.getStorageSync('token')) return
    if(this.data.finish){
      return 
    }
    if(this.data.loading){
      return 
    }
    this.setData({
      loading: true
    })
    let data = {
      page: this.data.page,
      type: 0
    }
    http.get('notice/getlist', data).then(res => {
      if(res.data.length === 0){
        this.setData({
          finish: true
        })
      }
      let arr = this.data.list.concat(res.data)
      this.setData({
        list: arr
      })
      
    }).finally(res => {
      this.setData({
        loading: false
      })
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.reload()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})