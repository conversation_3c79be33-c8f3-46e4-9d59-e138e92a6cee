<!--service/applyrefund/refund.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="state z-padding-lr-32" wx:if="{{state===0}}">
      <image class="icons z-margin-r-16" src="../../static/service/underway.png"></image>
      平台审核中
</view>
<view class="state refund z-padding-lr-32" wx:if="{{state==-1}}">
      <image class="icons z-margin-r-16" src="../../static/service/refused.png"></image>
      审核失败：{{note}}
</view>
<view class="state z-padding-lr-32" wx:if="{{state===1}}">
  <image class="icons z-margin-r-16" src="../../static/service/underway.png"></image>
      平台已同意退款
</view>
<view class="state z-padding-lr-32" wx:if="{{state===-2}}">
  <image class="icons z-margin-r-16" src="../../static/service/underway.png"></image>
      已取消退款
</view>
<view class="samebox z-padding-32 z-font-28 z-margin-tb-32 z-radius-24">
  <text class="text_999 ">服务状态</text>
  <view class="green z-font-w">{{info.orderLog[0].content}}</view>
</view>
<view class="samebox z-padding-32 z-font-28 z-margin-tb-32 z-radius-24" bindtap="selectReason">
  <text class="">退款原因</text>
  <view class="picker">
      {{reason||'请选择'}}
      <van-icon name="arrow" />
    </view>
</view>
<view class="samebox z-padding-32 z-font-28 z-margin-tb-32 z-radius-24">
  <text class="">退款金额</text>
  <input  class="refund_price" bindinput="setprice" value="{{refund_price}}" placeholder="输入退款金额(最多可申请{{payprice}})" type="digit" />
</view>
<view class="z-margin-32">
  <view class="z-font-28 z-font-w">补充说明</view>
  <view class="smcontent z-radius-24 z-padding-32 z-margin-tb-32">
    <textarea value="{{content}}" bindinput="setContent" placeholder="详细说明您遇到的问题，有助于平台更加地帮您解决" />
    <view class="z-flex-c image-box">
      <view class="z-margin-r-32 z-margin-b-24 each-img-min  images-box" wx:for="{{images}}"  data-index="{{index}}" bindtap="chooseImage">
          <image class="photo" src="{{tool.cdn(item)}}" mode="aspectFill"></image>
          <image src="/static/service/dels.png" class="del" catchtap="delImages" data-index="{{index}}"></image>
        </view>
        <view class="z-margin-r-32 z-margin-b-24 each-img-min" wx:if="{{images.length<9}}" bindtap="chooseImage">
          <image class="photo" src="/static/service/upload.png"></image>
        </view>
    </view>
    
    <!-- <image wx:for="{{images}}" src="{{tool.cdn(item)}}" class="icon" bindtap="chooseImage"></image> -->
    <!-- <image src="/static/service/upload.png" class="icon" bindtap="chooseImage"></image> -->
  </view>
</view>
<view class="z-font-28 text_999 z-margin-lr-32 notice" wx:if="{{config.refund_notice}}">{{config.refund_notice}}</view>
<van-popup position="bottom" show="{{show}}" close-on-click-overlay="true" bind:close="close">
    <van-picker show-toolbar title="退款原因" columns="{{ columns }}" value-key="name" bind:confirm="confirm"
      bind:cancel="close" />
  </van-popup>
<view class="z-padding-32" wx:if="{{state===''}}">
  <view class="z-btn" bindtap="resave">提交申请</view>
</view>
<view class="z-padding-32" wx:if="{{state===-1 || state===-2}}">
  <view class="z-btn" bindtap="resave">重新提交</view>
</view>
<view class="z-padding-32" wx:if="{{state===0}}">
  <view class="z-btn" bindtap="cancel">取消退款</view>
</view>