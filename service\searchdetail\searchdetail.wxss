/* pagesA/searchdetail/searchdetail.wxss */
.search_box{
width: 100vw;
height: 100vh;
background-color: #EDF1F4;
}
.search{
  width: 95vw;
  margin: 0 auto;
  box-sizing: border-box;
}
.van-cell__left-icon-wrap{
  margin-top: 8rpx;
}
.van-search__content{
  background-color: #fff !important;
}

.right-icon{
  color: #fff;
  background-color: #1782FA;
  border-radius: 40rpx;
  width: 90rpx;
  height: 58rpx;
}
.history{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.rubbish{
  width: 24rpx;
  height: 24rpx;
}
.history_box{
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  width: 90vw;
  box-sizing: border-box;
  margin: 20rpx auto;
}
.history_item{
  display: inline;
  background-color: #fff;
  margin-right: 24rpx;
}
.rankbox{
  width: 90vw;
  margin: 0 auto;
  box-sizing: border-box;
  background-color: #FCEEF0;
}
.rankimg{
  width: 200rpx;
  height: 95rpx;
}
.rankbox_top{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.rankbox_white{
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.rankbox_white_item{
  display: flex;
  align-items: center;
 width: 50%;
}
.one{
  background: linear-gradient(90deg, #E57C66 0%, #EE5439 100%);
  color: #fff;
}
.two{
  background: linear-gradient(90deg, #EAA544 0%, #E78939 100%);
  color: #fff;
}
.three{
  background: linear-gradient(90deg, #F7D359 0%, #F0BC51 100%);
  color: #fff;
}
.same{
  color: #fff;
  background-color: #D1B29D;
}
