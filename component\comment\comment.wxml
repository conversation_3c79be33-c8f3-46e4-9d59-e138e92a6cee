<!--component/comment/comment.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="comment  z-padding-24 z-radius-24 z-margin-b-24">
  <view class="comment_item_top">
    <view class="comment_item_top_left">
      <image class="comment_item_img z-margin-r-16" src="{{tool.cdn(info.user.avatar)}}" mode="" />
      <view class="comment_item_top_left_text">
        <view>匿名用户</view>
        <view class="z-margin-t-16 z-font-24 text_999">{{tool.formatTime(info.createtime)}}</view>
      </view>
    </view>
    <view class="comment_item_top_right">
      <van-rate value="{{ info.score }}" color='#FF9600' readonly bind:change="onChangerate" />
    </view>
  </view>
  <view class="lable-box z-margin-t-24">
    <view class="label z-flex z-margin-r-8 z-margin-b-8 z-padding-lr-24 z-font-24"
      wx:for="{{tool.split(info.comment_label)}}">{{item}}</view>
  </view>
  <view class="comment_content z-margin-tb-24 z-font-24 z-font-w">
    {{info.content}}
  </view>
  <view class="scroll_box">
    <image src="{{tool.cdn(item)}}" bindtap="previewImages" data-index="{{index}}" wx:for="{{info.images}}"
      wx:key="*this" class="scroll_box_img z-radius-24" mode="aspectFill" />
  </view>
</view>