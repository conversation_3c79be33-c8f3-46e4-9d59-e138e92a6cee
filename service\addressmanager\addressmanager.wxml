<!--service/addressmanager/addressmanager.wxml-->
<view class="address">
  <van-nav-bar
    title="地址管理"
    left-arrow
    bind:click-left="onClickLeft"
    custom-style="background-color: #c8e2ff;"
    title-style="width: 136rpx; height: 36rpx; font-family: PingFangSC, PingFang SC; font-weight: 600; font-size: 34rpx; color: #333333; line-height: 36rpx; text-align: right; font-style: normal;"
  />
  <view class="main-box">
    <scroll-view class="scroll-box z-padding-t-24" scroll-y="true" show-scrollbar="{{false}}" enhanced="true" bindscrolltolower="more">
      <view class="addressbox z-margin-b-24 z-padding-24 z-radius-16" wx:for="{{list}}" bind:tap="chooseaddress"
        wx:key="index" data-item="{{item}}">
        <view class="left_box">
          <view class="middle z-margin-l-24">
            <view class="text_999 z-font-26">{{item.province}}{{item.city}}{{item.district}}</view>
            <view class="z-font-w z-margin-tb-16 addressdetail ">
              <view class="hidden z-font-30">{{item.area}}{{item.address}}</view>
              <view class="moren z-font-24 z-padding-lr-8 z-radius-8 z-margin-l-16" wx:if="{{1===item.state}}">默认
              </view>
            </view>
            <view class="z-font-24 text_999">
              <text>{{item.name}} {{item.sex === 1 ? '先生' : '先生'}}</text>
              <text class="z-padding-l-24">{{item.mobile}}</text>
            </view>
          </view>
        </view>
        <image src="../../static/service/edit.png" class="right" mode="" data-info="{{item}}" catchtap="change" />
        <image src="../../static/service/del.png" class="right z-margin-l-32" mode="" data-id="{{item.id}}" data-index="{{index}}" catchtap="del" />
      </view>
      <van-empty description="暂无地址" wx:if="{{finish && list.length === 0}}" />
    </scroll-view>

  </view>

  <view class="addressbox_bottom z-padding-t-32"   style="padding-bottom: {{safeBottom}}" bind:tap="toaddnewaddress">
    <view class="btnbox">
      <van-button type="primary" color="#1782fa" round size='large'>新增地址</van-button>
    </view>
  </view>
</view>