/* service/skilldetail/skilldetail.wxss */
.swiper_box{
  height: 750rpx;
  position: relative;
}
.swiper_img{
  width: 100vw;
  height: 750rpx;
}
.tips{
  position: absolute;
  right: 50rpx;
  color: #fff;
  background: rgba(0,0,0,0.5);
  top: 650rpx;
}
.skillinfo{
  background: #FFFFFF linear-gradient(90deg, #F0FFF3 0%, #EDFDDE 100%);
  width: 686rpx;
  margin: 0 auto;
  box-sizing: border-box;
}
.skillinfo_top{
  display: flex;
  background-color: #fff;
  justify-content: space-between;
  align-items: center;
  border-radius: 16rpx 16rpx 0 0 ;
}
.time{
  color: var(--maingreencolor);
  display: flex;
}
.time_one{

  background: #E8F7EC;
}
.time_two{

  background: #F3FBF5;
}
.van-button{
  border-radius: 10rpx !important;
}
.skillinfo_tips{
  background-color: #fff;
  display: flex;
  align-items: center;
}
.skillinfo_tips_itme{
  background-color: #F5F7F9;
  height: 40rpx;
}
.skillinfo_tips_itme:nth-child(n+2){
  margin-left: 20rpx;
}
.shopbox{
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.shopname{
  display: flex;
  align-items: center;
  background-color: #FCF7EE;
  color: #DBAC54;
  height: 40rpx;
}
.shopimg{
  width: 25rpx;
  height: 25rpx;
}
.skillinfo_btoom{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.skillinfo_btoom_img{
  width: 20rpx;
  height: 20rpx;
}
.skillinfo_btoom_item{
  display: flex;
  align-items: center;
}
.scroll{
}

.comment_box{
  width: 90vw;
  margin: 0 auto;
  box-sizing: border-box;
}
.comment_box_top{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.comment_item{
  box-sizing: border-box;
}
.comment_item_top{
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.comment_item_top_left{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.comment_item_top_left_text{
  display: flex;
  flex-direction: column;
}

.comment_item_img{
  width: 66rpx;
  height: 66rpx;
  border-radius: 50%;
}
.scroll_box{
  width: 600rpx;
  white-space: nowrap;
}
.scroll_box_imgbox{
  display: flex;
}
.scroll_box_img{
  width: 190rpx;
  height: 190rpx;
}
.scroll_box_img:nth-child(n+2){
  margin-left: 30rpx;
}

.follow{
  background: #1782FA;
  width: 112rpx;
  height: 56rpx;
  color: #fff;
}

.followed{
  border: 1px solid #EDEEF1;
  box-sizing: border-box;
  width: 112rpx;
  height: 56rpx;
  color: #A4A9B7;
}