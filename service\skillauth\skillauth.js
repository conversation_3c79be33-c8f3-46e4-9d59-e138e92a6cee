const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    info: '',
    skill_id: '',
    canvasW: '',
    canvasH: '',
    canvasW1: '',
    canvasH1: '',
  },
  getInfo() {
    http.get('skill/getsampleskill', {
      id: this.data.skill_id,
    }).then(res => {
      this.setData({
        info: res.data,
      })
      this.addWaterMark(util.cdn(res.data.health_image),'#canvas-box')
      this.addWaterMark(util.cdn(res.data.certificate_image),'#canvas-box1')
    })
  },
  addWaterMark(url,view) {
    let that = this
    wx.getImageInfo({
      src: url,
      success:(response) => {
        const imgW = response.width
        const imgH = response.height
        const SelectorQuery = wx.createSelectorQuery().in(that)
        SelectorQuery.select(view).fields({
          node: true,
          size: true
        })
        SelectorQuery.exec((res) => {
          const canvasBox = res[0].node
          const canvasW = res[0].width
          const canvasH = imgH / imgW * res[0].width
          if(view == '#canvas-box'){
            that.setData({
              canvasW,
              canvasH
            })
          }else {
            that.setData({
              canvasW1: canvasW,
              canvasH1: canvasH
            })
          }
          
          const ctx = canvasBox.getContext('2d')
          const dpr = wx.getSystemInfoSync().pixelRatio
          canvasBox.width = canvasW * dpr
          canvasBox.height = canvasH * dpr
          ctx.scale(dpr, dpr)
          const img = canvasBox.createImage()
          img.src = url
          img.onload = () => {
            ctx.clearRect(0, 0, canvasW, canvasH)
            ctx.drawImage(img, 0, 0, canvasW, canvasH)
            let watermark = app.globalData.config.user_name || 'AJAX家政';
            let fontColor = "#ffffff";
            let fontSize = 12;
            let lineHeight = 30;
            let watermarkWidth = watermark.length*12; 
            let maxLength = canvasH > canvasW ? canvasH * 2 : canvasW;
            ctx.save();
            ctx.beginPath();
            ctx.translate(canvasW / 2, -maxLength);
            ctx.rotate(45 * Math.PI / 180);
            let xNum = Math.ceil(maxLength * 2 / watermarkWidth);
            let yNum = Math.ceil(maxLength * 2 / lineHeight);
            for (let j = 0; j < yNum; j++) {
              ctx.fillStyle = fontColor
              ctx.font = `${fontSize}px serif`;
              ctx.fillText(watermark, 0, lineHeight * j);
              for (let i = 1; i < xNum; i++) {
                ctx.fillStyle = fontColor
                ctx.font = `${fontSize}px serif`;
                ctx.fillText(watermark, watermarkWidth * i, lineHeight * j);
              }
            };
            ctx.closePath()
            ctx.restore()
          }
        })
      },
      fail:(e)=>{
        console.log(e)
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      skill_id: options.skill_id
    })
    this.getInfo()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})