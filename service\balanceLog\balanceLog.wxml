<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="balanceLog">
  <view class="z-flex-0">
    <van-tabs ellipsis="{{false}}" sticky color="#1782FA" active="{{ active }}" bind:change="onChange">
      <van-tab title="消费记录">
      </van-tab>
      <van-tab title="充值记录">
      </van-tab>
    </van-tabs>
  </view>
  <view class="main-box">
    <scroll-view class="scroll-box" scroll-y="true" show-scrollbar="{{false}}" enhanced="true" bindscrolltolower="more">
      <block wx:if="{{active == 0}}">
        <view class="each z-margin-t-24 z-padding-lr-32 z-padding-tb-24" wx:for="{{list}}">
          <view>
            <view class="text-b">{{item.memo}}</view>
            <view class="text-tint z-margin-t-8"></view>
          </view>
          <view>
            <view class="text-b z-text-r" wx:if="{{tool.includes(item.money,'-')}}">{{item.money}}</view>
            <view class="text-b z-text-r" wx:else>+{{item.money}}</view>
            <view class="text-tint z-margin-t-8 z-font-22">{{tool.formatTime(item.createtime)}}</view>
          </view>
        </view>
      </block>
      <block wx:else>
        <view class="each z-margin-t-24 z-padding-lr-32 z-padding-tb-24" wx:for="{{list}}">
          <view>
            <view class="text-b">充值方式：微信小程序</view>
            <view class="text-tint z-margin-t-8 z-font-22" wx:if="{{item.paytype == 0 }}" bindtap="copy" data-content="{{item.orderId}}">订单：{{item.orderId}}</view>
          </view>
          <view>
            <view class="text-b z-text-r">{{item.price}}</view>
            <view class="text-tint z-margin-t-8 z-font-22">{{tool.formatTime(item.createtime)}}</view>
          </view>
        </view>
      </block>
      <van-empty description="暂无记录" wx:if="{{finish && list.length === 0}}" />
    </scroll-view>
  </view>
</view>