// service/kouling/kouling.js
import http from "../../utils/http"
import util from "../../utils/util"
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    code: '',
    safeBottom: `30px`,
    exchange_info_id: ''
  },
  rule(){
    util.skip(`/service/info/info?id=${this.data.exchange_info_id}&name=兑换规则`)
  },
  onLoadConfig(option){
    this.setData({
      exchange_info_id: app.globalData.config['exchange_info_id'],
    })
  },
  setCode(e) {
    this.setData({
      code: e.detail.value
    })
  },
  couponTap(e){
    console.log(e)
    http.get('coupon/receive', {
      type: 1,
      code: this.data.code
    }).then(res => {
      util.toast(res.msg)
    })
   
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const high = app.globalData.safeBottom
    this.setData({
      safeBottom: `${high}px`
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})