// service/feedback/feedback.js
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    images: [],
    mobile: '',
    content: ''
  },
  save(){
    if (this.data.content.trim() === '') return util.toast('请输入您的反馈意见')
    if(this.data.mobile){
      if(!/^1\d{10}$/.test(this.data.mobile)) return util.toast('请输入正确的手机号')
    }
    
    http.post('feedback/submitfeedback',{
      mobile: this.data.mobile,
      content: this.data.content,
      images: this.data.images.join(','),
      type: 0
    },true).then(res => {
      util.toast(res.msg)
      setTimeout(()=>{
        util.back()
      },1000)
    })
  },
  setContent(e){
    this.setData({
      content: e.detail.value
    })
  },
  setMobile(e){
    this.setData({
      mobile: e.detail.value
    })
  },
  delImages(e){
    let index = e.currentTarget.dataset.index
    this.data.images.splice(index,1)
    this.setData({
      images: this.data.images
    })
  },
  chooseImages(e) {
    let index = e.currentTarget.dataset.index
    http.chooseImg(['album', 'camera'], true, true).then(res => {
      let arr = this.data.images;
      if(index !== undefined){
        arr.splice(index,1,res.data.url)
      }else {
        arr.push(res.data.url)
      }
      this.setData({
        images: arr
      })
    })
  },
  on

// service/feedback/feedback.js
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    images: [],
    mobile: '',
    content: ''
  },
  save(){
    if (this.data.content.trim() === '') return util.toast('请输入您的反馈意见')
    if(this.data.mobile){
      if(!/^1\d{10}$/.test(this.data.mobile)) return util.toast('请输入正确的手机号')
    }
    
    http.post('feedback/submitfeedback',{
      mobile: this.data.mobile,
      content: this.data.content,
      images: this.data.images.join(','),
      type: 0
    },true).then(res => {
      util.toast(res.msg)
      setTimeout(()=>{
        util.back()
      },1000)
    })
  },
  setContent(e){
    this.setData({
      content: e.detail.value
    })
  },
  setMobile(e){
    this.setData({
      mobile: e.detail.value
    })
  },
  delImages(e){
    let index = e.currentTarget.dataset.index
    this.data.images.splice(index,1)
    this.setData({
      images: this.data.images
    })
  },
  chooseImages(e) {
    let index = e.currentTarget.dataset.index
    http.chooseImg(['album', 'camera'], true, true).then(res => {
      let arr = this.data.images;
      if(index !== undefined){
        arr.splice(index,1,res.data.url)
      }else {
        arr.push(res.data.url)
      }
      this.setData({
        images: arr
      })
    })
  },
  onClickLeft(){
    util.back()
  },
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})