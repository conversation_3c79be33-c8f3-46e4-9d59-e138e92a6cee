// app.js
import CustomHook from 'spa-custom-hooks';
import http from "./utils/http"
let globalData = {
  //位置
  realAddress: '',
  address: '',
  // 是否已拿到token
  token: '',
  
  //顶部区域
  safeTop: '',
  safeBottom: '',
  menuH: '',
  // 用户信息
  user: '',
  userInfo: '',
  
  config: '',
  
  templateconfig: ''
}

CustomHook.install({
  'Config':{
    name: 'Config',
    watchKey: 'config',
    onUpdate(val) {
      return !!val;
    }
  },
  'Address':{
    name:'Address',
    watchKey: 'address',
    onUpdate(val){
      return !!val;
    }
  },
 }, globalData || 'globalData')

App({
  onLaunch() {
    const res = wx.getMenuButtonBoundingClientRect()
    this.globalData.safeTop = wx.getStorageSync('safeTop') ? wx.getStorageSync('safeTop') : res.top
    this.globalData.menuH = wx.getStorageSync('menuH') ? wx.getStorageSync('menuH') : res.height
    if(!wx.getStorageSync('safeTop')){
      wx.setStorageSync('safeTop', this.globalData.safeTop)
    }
    if(!wx.getStorageSync('menuH')){
      wx.setStorageSync('menuH', this.globalData.menuH)
    }
    this.globalData.safeBottom = wx.getStorageSync('safeBottom') ? wx.getStorageSync('safeBottom') : (wx.getSystemInfoSync().safeArea.bottom - wx.getSystemInfoSync().safeArea.height)
    if(!wx.getStorageSync('safeBottom')){
      wx.setStorageSync('safeBottom', this.globalData.safeBottom)
    }
    http.get('index/getconfig').then(res => {
      this.globalData.config = res.data
    })
    http.get('index/templateconfig').then(res => {
      this.globalData.templateconfig = res.data
    })
  },
  onShow(){
    // 展示本地存储能力
    
    http.getLocation().then(res => {
      
      this.globalData.address = res
      this.globalData.realAddress = res
    }).catch(e => {
      
      this.globalData.address = e
    })
  },
  globalData
})

