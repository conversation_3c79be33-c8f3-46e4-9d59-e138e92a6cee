/* pagesA/searchresult/searchresult.wxss */
.searchresult{
  background-color: #EDF1F4;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.container_top{
  background-color: #fff;
  flex-shrink: 0;
}

.screen-box{

}
.rank-icon{
  width: 12rpx;
}
.screen-word{
  color: #A4A9B7;
}
.active .screen-word{
  color: #1C274C;
}
.main-box{
  flex: 1;
  overflow: hidden;
}
.search{
  width: 95vw;
  margin: 0 auto;
  box-sizing: border-box;
}
.right-icon{
  color: #fff;
  background-color: #1782FA;
  border-radius: 40rpx;
  width: 90rpx;
  height: 58rpx;
}
.van-cell__left-icon-wrap{
  margin-top: 8rpx;
}
.van-dropdown-menu {
  box-shadow: var(--dropdown-menu-box-shadow,0 0px 0px rgba(0, 0, 0, 0)) !important;
}
.scroll{
  height: 100%;
  box-sizing: border-box;
}


.service-box{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.icons{
  width: 32rpx;
  height: 32rpx;
}