// pages/mytest/mytest.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    safeTop: `40px`,
    info: '',
    config: '',
    privacy_id: '', // 隐私政策ID
    agreement_id: '' // 用户协议ID
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const high = app.globalData.safeTop + 50
    this.setData({
      safeTop: `${high}px`
    })
    this.getAgreementIds()
  },
    /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getInfo();
    this.onLoadConfig();
  },
  onLoadConfig(option){
    this.setData({
      config: app.globalData.config,
      agreement_id: app.globalData.config['user_agreement_info_id'],
      privacy_id: app.globalData.config['user_privacy_info_id']
    })
  },
  toService(){
    wx.navigateToMiniProgram({
      appId: 'wxc2e2e6963f516a2b',
      envVersion: 'develop'
    })
  },
  toShop(){
    wx.navigateToMiniProgram({
      appId: 'wxc71bf1affdb7c8e5',
      envVersion: 'develop'
    })
  },
  login(){
    util.skip('/service/login/login')
  },
  skip(e){
    let url = e.currentTarget.dataset.url
    util.authSkip(url)
  },
  myOrder(e){
    let type = e.currentTarget.dataset.type
    let active = e.currentTarget.dataset.active
    let num = this.data.info ? this.data.info.orderCount.unServiceCount : 0
    util.authSkip('/pages/myorder/myorder?type='+type+'&active='+active+'&num='+num)
  },
  getInfo(){
    if(!wx.getStorageSync('token')) return
    http.post('index/userindex', '', !this.data.info).then(res => {
      this.setData({
        info: res.data
      })
    })
  },
  //跳转个人资料
  togrzl(){
    wx.navigateTo({
      url: '/service/myinfo/myinfo',
    })
  },
  //跳转意见反馈
  tofeedback(){
    wx.navigateTo({
      url: '/service/feedback/feedback',
    })
  },
  //跳转领券中心
  tocouponscenter(){
    wx.navigateTo({
      url: '/service/couponscenter/couponscenter',
    })
  },
  //跳转我的评论
  tomycomment(){
    wx.navigateTo({
      url: '/service/mycomment/mycomment',
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {}
  },
  
  //退出登录
  out(){
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('token')
          wx.removeStorageSync('userInfo')
          this.setData({
            info: ''
          })
          wx.showToast({
            title: '退出成功',
            icon: 'success'
          })
        }
      }
    })
  },
  
  //获取协议ID
  getAgreementIds(){
    this.setData({
      agreement_id: app.globalData.config['user_agreement_info_id'],
      privacy_id: app.globalData.config['user_privacy_info_id']
    })
  }


  
})