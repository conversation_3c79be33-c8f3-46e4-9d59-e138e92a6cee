<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="makeorder">
  <van-nav-bar
    title="预约下单"
    left-arrow
    bind:click-left="onClickLeft"
    custom-style="background-color: #c8e2ff;"
    title-style="width: 136rpx; height: 36rpx; font-family: PingFangSC, PingFang SC; font-weight: 600; font-size: 34rpx; color: #333333; line-height: 36rpx; text-align: right; font-style: normal;"
  />
  <view class="main-box">
    <view class="mytop z-margin-t-32">
      <view class="nav">
        <view bind:tap="change" data-index="0" class="item {{current==0 ? 'active' : ''}}">{{to_shop !== 2 ? '上门服务' :
          ''}}
        </view>
        <view bind:tap="change" data-index="1" class="item {{current==1 ? 'active' : ''}}">{{to_shop !== 1 ? '到店服务' :
          ''}}
        </view>
      </view>
      <view class="nav_content">
        <view class="haveaddaddress z-margin-tb-32" wx:if="{{current==0 && address}}" bind:tap="toaddress">
          <view class="z-font-w">
            {{address.province}}{{address.city}}{{address.district}}{{address.area}}{{address.address}}</view>
          <view class="addressname z-margin-t-32">
            <view class="addressname_left text_999 z-font-24">
              <view class="z-margin-r-24">{{address.name}} {{address.sex === 1 ? '先生' : '先生'}}</view>
              <view>{{address.mobile}}</view>
            </view>
            <view class="addressname_right">
              <view class="z-font-24">修改地址</view>
              <van-icon name="arrow" />
            </view>
          </view>
        </view>
        <view class="haveaddaddress z-margin-tb-32" wx:elif="{{current==0}}" bind:tap="toaddress">
          <view class="z-flex z-padding-40">
            <image src="../../static/service/addaddress.png" class="add"></image>
            <view class="z-font-30 text_999">选择地址</view>
          </view>
        </view>
        <view class="shopaddaddress z-margin-tb-32" wx:else="{{current==1}}">
          <text class="shop_name z-padding-lr-8 z-padding-tb-4 z-radius-8 z-font z-margin-r-24 z-font-24">店铺</text>
          <text class="z-font-w shop_address">{{shop.province}}{{shop.city}}{{shop.district}}{{shop.address}}</text>
        </view>
        <van-divider />
        <block wx:if="{{current==0 && skillInfo}}">

          <view class="z-flex-c z-margin-b-24">
            <view class="z-font-22">服务人员</view>
            <view class="z-flex-1"></view>
            <image src="{{tool.cdn(skillInfo.image)}}" class="avatar"></image>
            <view class="time z-font-24 z-margin-lr-24">
              {{skillInfo.name}}
            </view>
          </view>
        </block>
        <view class="servetime z-margin-b-16" wx:if="{{current==0}}" bindtap="chooseTime">
          <view class="z-font-22">请选择上门时间</view>
          <view class="time z-font-24">
            <view>{{timeInfo.starttime ? tool.formatTime(timeInfo.starttime) : '请选择上门时间'}}</view>
            <van-icon name="arrow" color="#999" size="24rpx" />
          </view>
        </view>
        <view class="servetime z-margin-b-16" wx:else="{{current==1 && shop.trade_hour}}">
          <view class="z-font-24">营业时间</view>
          <view class="time">
            <view class="z-font-24">{{shop.trade_hour}}</view>
          </view>
        </view>
      </view>
    </view>

    <view class="goodsinfo z-padding-24 z-radius-16 z-margin-tb-32">
      <view class="goodsinfo_top">
        <image src="{{tool.cdn(info.image)}}" class="goodsimg z-radius-16 z-margin-r-16" mode="aspectFill" />
        <view class="goodsinfo_top_right">
          <view class="goodsinfo_top_right_top">
            <view class="tip  z-flex  z-margin-r-8 z-font-18" wx:if="{{info.type == 0}}">严选</view>
            <view class="z-font-w">{{info.name}}</view>
          </view>
          <view class="tips_box" wx:if="{{sku}}">
            <view class="tips_item z-font-24 text_999 z-padding-tb-8 z-padding-lr-16 z-radius-8"
              wx:for="{{tool.split(sku.name)}}" wx:key="*this">
              {{item}}
            </view>
          </view>
          <view class="goodsinfo_top_right_bottom">
            <view class="goodsinfo_top_right_bottom_left">
              <view class="goodsinfo_top_right_bottom_left_left">
                <view>￥</view>
                <view class="z-font-w">{{price}}</view>
              </view>
            </view>
            <view class="goodsinfo_top_right_bottom_right">
              <van-stepper theme='round' value="{{ num }}" bind:change="numChange" />
            </view>
          </view>
        </view>
      </view>
      <view class="z-flex-c z-margin-t-32" wx:if="{{discount < 100}}">
        <view class="z-font-26">会员价</view>
        <view class="z-flex-1"></view>
        <text class="money">￥{{tool.toFix(price*num * discount /100)}}</text>
        <view class="discount z-flex z-font-24 z-padding-lr-8 z-margin-l-24">{{discount/10}}折</view>
      </view>
      <view class="yhq_box z-margin-tb-24" bindtap="couponTap">
        <view class="yhq_box_left">
          <image src="/static/service/yhq.png" class="yhq_img" mode="" />
          <view class="z-margin-l-24 z-font-26">优惠券</view>
        </view>
        <view class="yhq_box_right" wx:if="{{coupon}}">
          <view>-</view>
          <view class="money">￥{{coupon.reduce}}</view>
        </view>
        <view class="yhq_box_right" wx:else>
          <view class="z-font-24">请选择</view>
          <van-icon name="arrow" size="24rpx" />
        </view>
      </view>
      <van-divider />
      <view class="goodsinfo_bottom">
        <view class="text_999 z-font-24">合计：</view>
        <view class="z-font-w goodsinfo_bottom_money">
          <view class="z-font-24">
            ￥
          </view>
          <view class="z-font-32" wx:if="{{coupon}}">{{tool.toFix(price*num * discount /100 - coupon.reduce)}}</view>
          <view class="z-font-32" wx:else>{{tool.toFix(price*num * discount /100)}}</view>
        </view>
      </view>
    </view>
    <view class="travel-box z-margin-32 z-radius-24 z-padding-32" wx:if="{{travel}}">
      <view class="travel-t z-flex-c">
        <view class="z-font-30">出行方式</view>
        <view class="z-flex-1"></view>
        <view class="travels z-flex-c">
          <view class=" travel z-flex z-margin-r-24 z-radius-8 z-font-24 text_999 {{traveltype == 1 ? 'choosed' : ''}}"
            data-type="{{1}}" bindtap="chooseTravel">滴滴/出租</view>
          <view class="travel z-flex z-radius-8  text_999 z-font-24  {{traveltype == 2 ? 'choosed' : ''}}"
            data-type="{{2}}" bindtap="chooseTravel">公交/地铁</view>
        </view>
      </view>
      <view class="z-font-22 text_999 z-margin-t-24" wx:if="{{traveltype == 1}}">
        全程共{{travel.distance}}公里，出租出行收取来回费用，白天起步{{travel.cityConfig.init_price}}元，超过{{travel.cityConfig.init_distance}}公里部分每公里{{travel.cityConfig.add_price}}元
      </view>
      <view class="z-font-22 text_999 z-margin-t-24">
        公交出行免费，当前城市{{travel.cityConfig.bus_start}}点-{{travel.cityConfig.bus_end}}点可选择公交地铁出行方式
      </view>
      <view class="z-flex-c z-margin-t-24">
        <view class="z-font-24">车费</view>
        <view class="z-flex-1"></view>
        <view class="z-font-24 money">¥{{travel.distance_price}}</view>
      </view>
    </view>
    <view class="order_bz z-margin-tb-32 z-padding-32 z-radius-16">
      <view class="z-margin-b-32 title" >
        故障描述
      </view>
      <textarea value="{{memo}}" class="memo" bindinput="setMemo" maxlength='-1' auto-height="true"
        placeholder="填写故障描述信息" />
    </view>
    <view class="z-margin-32 z-font-w" wx:if="{{totalMoney > 0}}">
      支付方式
    </view>
    <view class="pay_box z-margin-tb-32 z-padding-tb-32 z-padding-lr-24 z-radius-16" bind:tap="changepay" data-index="0"
      wx:if="{{totalMoney > 0}}">
      <view class="left">
        <image src="/static/service/wx.png" class="wx_img z-margin-r-24" mode="" />
        <view>微信</view>
      </view>
      <image src="../../static/login/choose.png" wx:if="{{paytype != 0}}" class="choosed_img" mode="" />
      <image src="../../static/login/choosed.png" wx:else class="choosed_img" mode="" />

    </view>
    <view class="pay_box z-margin-tb-32 z-padding-tb-32 z-padding-lr-24 z-radius-16" bind:tap="changepay" data-index="4"
      wx:if="{{totalMoney > 0}}">
      <view class="left">
        <image src="/static/service/balance.png" class="wx_img z-margin-r-24" mode="" />
        <view>余额</view>
        <view class="z-font-26 text_666">（当前余额：￥{{money}}）</view>
      </view>
      <image src="../../static/login/choose.png" wx:if="{{paytype != 4}}" class="choosed_img" mode="" />
      <image src="../../static/login/choosed.png" wx:else class="choosed_img" mode="" />
    </view>
  </view>
  <view class="bottom-box"  wx:if="{{totalMoney > 0}}">
    <view class="order_bottom z-padding-lr-32 z-padding-t-32" style="padding-bottom: {{safeBottom}}">

      <view class="z-flex-c">
        <view class="text_999"> 总计：</view>
        <view class="order_bottom_money">
          <view class="z-font-30">￥</view>
          <view class="z-font-w z-font-40">{{totalMoney}}</view>
        </view>
      </view>

      <view class="order_bottom_right z-raius-24" bindtap="repay">
        <van-button type="primary" color="#1782fa" round size='large'> 立即支付</van-button>
      </view>
    </view>
  </view>
  <view class="bottom-box"  wx:elif="{{totalMoney !== '' && ((to_shop !== 2 && current==0 && address) || (to_shop !== 1 && current==1))}}">

    <view class="order_bottom z-padding-lr-32 z-padding-t-32" style="padding-bottom: {{safeBottom}}">

      <view class="z-flex-c">
        <view class="text_999"> 免支付</view>
      </view>

      <view class="order_bottom_right z-raius-24" bindtap="repay">
        <van-button type="primary" color="#1782fa" round size='large'> 立即预约</van-button>
      </view>
    </view>
  </view>
  <van-popup show="{{ show }}" position="bottom" bind:close="onClose">
    <view class="time-box">
      <view class="z-font-32 z-padding-24 z-text-c">选择服务时间</view>
      <view class="z-flex-c z-marigin-32">
        <block wx:for="{{dayTime}}">
          <view class="z-flex-1 z-flex-y-c {{index == day ?  'today' : ''}}" data-index="{{index}}" bindtap="changeDay">
            <view class="day-t z-font-30">{{item.day}}</view>
            <view class="day-b z-font-22">{{item.time}}</view>
          </view>
        </block>

      </view>
      <view class="times-box  z-padding-32">
        <block wx:for="{{list}}">
          <block wx:if="{{skill_id}}">
            <view wx:if="{{(item.starttime - now) >=0}}"
              class="times  z-flex-y-c z-radius-24 {{item.state === 1 ? 'active' : '' }} {{item.state === 2 ? 'disable' : '' }} {{item.starttime == timeInfo.starttime ? 'choosed' : '' }}"
              bindtap="choose" data-info="{{item}}">
              <view class="times-t z-font-40 z-font-w">{{tool.formatTime(item.starttime, 'HH:MM')}}</view>
              <view class="times-b z-font-22" wx:if="{{item.starttime == timeInfo.starttime}}">当前选择</view>
              <view class="times-b z-font-22" wx:elif="{{item.state === 1}}">已预约</view>
              <view class="times-b z-font-22" wx:elif="{{item.state === 2}}">不可预约</view>
              <view class="times-b z-font-22" wx:else>可预约</view>
            </view>
          </block>
          <block wx:else>
            <view
              wx:if="{{(item.starttime - now) >=0 && tool.getHours(item.starttime) >= info.start_hour && tool.getHours(item.starttime) < info.end_hour}}"
              class="times z-radius-24 {{item.state === 1 ? 'active' : '' }}  {{item.state === 2 ? 'disable' : '' }} {{item.starttime == timeInfo.starttime ? 'choosed' : '' }}"
              bindtap="choose" data-info="{{item}}">
              <view class="times-t z-font-40 z-font-w">{{tool.formatTime(item.starttime, 'HH:MM')}}</view>
              <view class="times-b z-font-22" wx:if="{{item.starttime == timeInfo.starttime}}">当前选择</view>
              <view class="times-b z-font-22" wx:elif="{{item.state === 1}}">已预约</view>
              <view class="times-b z-font-22" wx:elif="{{item.state === 2}}">不可预约</view>

              <view class="times-b z-font-22" wx:else>可预约</view>
            </view>

          </block>
        </block>
        <view wx:if="{{day == 0 && today == 1}}" class="z-flex-c text_666 z-font-24 z-margin-t-72 notime">今日暂无可约时段
        </view>
      </view>
      <view class="z-btn z-margin-32" bindtap="onClose">确定</view>
    </view>
  </van-popup>
</view>