<!--service/orderdetail/orderdetail.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="orderdetail">
  <view class="main-box">
    <view class="box_top z-margin-lr-32 z-margin-t-32 z-margin-b-8">
      <view class="z-font-w title">{{info.orderLog[0].content}}</view>
      <view bind:tap="clickshow"> <text>状态跟踪</text>
        <van-icon class="z-padding-l-16" name="arrow-up" wx:if="{{show}}" />
        <van-icon class="z-padding-l-16" name="arrow-down" wx:else />
      </view>
    </view>
    <view class="z-margin-lr-32 time z-font-22">{{tool.formatTime(info.orderLog[0].createtime)}}</view>


    <van-transition show="{{ show }}" name="fade-down">
      <view class="status-box z-margin-32">
        <view class="status-top">
          <image src="/static/service/orderstatus.png" class="icon"></image>
          <view class="z-font-24">状态跟踪</view>
        </view>
        <view class="status-list  z-radius-b-20">
          <view class="list z-padding-32" wx:for="{{info.orderLog}}">
            <view class="circle z-margin-r-32"></view>
            <view class="status">
              <view class="z-font-26 z-margin-b-8">{{item.content}}</view>
              <view class="z-font-22 text_666">{{tool.formatTime(item.createtime)}}</view>
            </view>
          </view>
        </view>
      </view>
    </van-transition>

    <view class="order_content z-padding-32">
      <block wx:if="{{info.to_shop === 'shop'}}">
        <view class="ercode_box z-margin-tb-32 z-padding-32 z-radius-24">
          <view class="ercode_box_top">
            <image src="{{tool.cdn(info.detail.image)}}" class="ercode_box_img z-radius-24 z-margin-r-24" mode="aspectFill" />
            <view class="ercode_box_top_right">
              <view class="ercode_box_top_right_item">
                <!-- <view class="fourbox_name_left  z-flex  z-margin-r-8 z-font-18">
                  严选
                </view> -->
                <view>{{info.detail.name}}</view>
              </view>
              <view class="z-font-24 text_666"></view>
              <view class="money">￥{{info.detail.price}}</view>
            </view>
          </view>
          <van-divider dashed />
          <view class="z-flex-y-c">
            <view class="z-font-32"  data-content="{{info.check_name}}" bind:tap="copyclick">核销码：{{info.check_name}}</view>
            <image src="{{tool.cdn(info.qrcode_image,1)}}" class="ercodeimg" mode="aspectFill" />
          </view>
        </view>
        <view class="newshop_info z-margin-tb-32 z-padding-32 z-radius-24">
          <view>{{info.shopInfo.abbr}}</view>
          <view class="z-margin-t-16 newshop_info_addressbox">
            <view class="text_999 z-font-24 z-flex-1 z-padding-r-24">
              <view>{{info.shopInfo.province}}{{info.shopInfo.city}}{{info.shopInfo.district}}{{info.shopInfo.address}}
              </view>
              <view class="z-margin-t-8">营业时间：{{info.shopInfo.trade_hour}}</view>
            </view>
            <view class="newshop_info_addressbox_right">
              <view class="line"></view>
              <view class="newshop_info_addressbox_item z-padding-lr-32"  bindtap="openLoaction">
                <image src="../../static/index/shopposition.png" class="position" mode="" />
                <view class="text_999 z-font-24 z-margin-t-16">导航</view>
              </view>
              <view class="newshop_info_addressbox_item"  bindtap="call" data-content="{{info.shopInfo.leader_mobile}}">
                <image src="../../static/index/phone.png" class="position" mode="" />
                <view class="text_999 z-font-24 z-margin-t-16">电话</view>
              </view>
            </view>
          </view>
        </view>
      </block>
      <block wx:else>
        <view class="order_info z-padding-24 z-radius-24">
          <view class="order_content_time  z-flex z-font-22">
            预约时间
          </view>
          <view class="order_content_time_text z-font-w z-margin-tb-32">
            {{tool.formatTime(info.starttime, 'mm:dd')}} <text class="hour">{{tool.formatTime(info.starttime,
              'HH:MM')}}</text>
          </view>
          <view class="address z-font-w">
            {{info.orderAddress.province}}{{info.orderAddress.city}}{{info.orderAddress.district}}{{info.orderAddress.address}}
          </view>
          <view class="my_box z-margin-tb-32 z-font-28 text_999">
            <view><text
                class="z-padding-r-16">{{info.orderAddress.name}}</text><text>{{info.orderAddress.mobile}}</text></view>
          </view>
        </view>
        <view class="sikll_box z-margin-tb-32 z-padding-32 z-radius-24" wx:if="{{info.skillInfo}}">
          <view>服务者</view>
          <view class="z-flex-1"></view>
          <view class="sikll_box_right">
            <image src="{{tool.cdn(info.skillInfo.image)}}" class="sikll_box_img" mode="" />
            <view class="z-margin-l-16">{{info.skillInfo.name}}</view>
          </view>
          <image class="phone z-margin-l-32" bindtap="call" data-content="{{info.skillInfo.mobile}}" src="/static/service/phone.png"></image>
        </view>
      </block>

      <view class="shop_info z-margin-tb-32 z-padding-32 z-radius-24">
        <view class="project z-margin-tb-32">
          <image src="{{tool.cdn(info.detail.image)}}" class="project_img z-radius-24 z-margin-r-24" mode="aspectFill" />
          <view class="project_right">
            <view>{{info.detail.name}}</view>
            <view class="project_right_bottom">
              <view><text class="money z-font-w">¥{{info.detail.price}}</text> <text class="z-font-28 text_999"></text></view>
              <view class="text_999 z-font-24">×{{info.detail.num}}</view>
            </view>
          </view>
        </view>

        <van-divider />
        <view class="z-flex-c z-margin-t-32" wx:if="{{info.discount < 100}}">
          <view class="z-font-26">会员价</view>
          <view class="z-flex-1"></view>
          <text class="money">￥{{tool.toFix(info.price * info.discount /100)}}</text>
          <view class="discount z-flex z-font-24 z-padding-lr-8 z-margin-l-24">{{info.discount/10}}折</view>
        </view>
        <view class="coupons_box" wx:if="{{info.coupon_price > 0}}">
          <view class="coupons_box_left z-font-26">
            <image src="/static/service/yhq.png" class="yhq_img z-margin-r-16" mode="" />
            <view class="z-font-26">优惠券</view>
          </view>
          <view>
            <text class="money z-font-26">-￥{{info.coupon_price}}</text>
          </view>
        </view>
        <view class="coupons_box z-margin-tb-32" wx:if="{{info.premium_price}}">
          <view class="coupons_box_left">
            <view class="z-font-26">补差价</view>
          </view>
          <view>
            <text class="z-font-26">￥{{info.premium_price}}</text>
          </view>
        </view>
        <view class="coupons_box z-margin-tb-32" wx:for="{{info.adddetail}}">
          <view class="coupons_box_left">
            <view class="z-font-26">加项（{{item.name}}/{{item.cost_seconds}}分钟/数量:{{item.num}}）</view>
          </view>
          <view>
            <text class="z-font-26">￥{{tool.toFix(item.price*item.num)}}</text>
          </view>
        </view>
        <view class="coupons_box z-margin-tb-32" wx:if="{{info.travel_price}}">
          <view class="coupons_box_left">
            <view class="z-font-26">车费</view>
          </view>
          <view>
            <text class="z-font-26">￥{{info.travel_price}}</text>
          </view>
        </view>
        <van-divider />
        <view class="all">
          <text class="z-font-24 text_999">合计：</text>
          <text class="z-font-w  z-font-32">￥{{info.payprice}}</text>
        </view>
        <view class="all z-margin-t-16" wx:if="{{info.refund_price > 0}}">
          <text class="z-font-24 text_999">（退款金额：</text>
          <text class="refund z-font-24">￥{{info.refund_price}}</text>
          <text class="z-font-24 text_999">）</text>
        </view>
      </view>
      <view class="orderinfo z-margin-tb-32 z-padding-32 z-radius-24">
        <view class="orderinfo_item">
          <view class="text_999 z-font-24">订单编号：</view>
          <view class="z-font-24">
            <text class="z-padding-r-16">{{info.orderId}}</text>
            <text class="z-font-24 copy  z-padding-lr-8 z-radius-8" data-content="{{info.orderId}}"
              bind:tap="copyclick">复制</text>
          </view>
        </view>
        <view class="orderinfo_item z-margin-tb-32">
          <view class="text_999 z-font-24">下单时间：</view>
          <view class="z-font-24">
            <text class="z-padding-r-16">{{tool.formatTime(info.paytime)}}</text>
          </view>
        </view>
        <view class="orderinfo_item z-margin-tb-32">
          <view class="text_999 z-font-24">支付方式：</view>
          <view class="z-font-24">
            <text class="z-padding-r-16">{{info.paytype === 4 ? '余额' : '微信'}}</text>
          </view>
        </view>
        <view class="orderinfo_item z-margin-tb-32">
          <view class="text_999 z-font-24">备注：</view>
          <view class="z-font-24">
            <text class="z-padding-r-16">{{info.memo}}</text>
          </view>
        </view>
      </view>
    </view>

  </view>
  <view class="order_bottom z-padding-lr-32 z-padding-t-32" style="padding-bottom: {{safeBottom}}">
    <view class="order_bottom_left" bind:tap="toreport"  wx:if="{{info.status > 0}}">
      <image src="/static/service/warring.png" class="warring_img" mode="" />
      <view class="text_999 z-font-24 z-margin-t-16">举报</view>
    </view>
    <view class="z-flex-1" wx:else></view>
    <view class="order_bottom_right">
      <van-button type="primary" class="z-margin-r-32" color="#1782fa" round wx:if="{{info.status === 0}}" bindtap="waitpay">立即支付</van-button>
      <van-button plain class="z-margin-r-32" bind:tap="toapplyrefund" wx:if="{{info.status > 0  && info.is_service !== 2 && info.is_settle < 2}}">申请退款</van-button>
      <van-button type="primary" class="z-margin-r-32" color="#1782fa" round wx:if="{{info.status > 1 && info.status < 6}}" bindtap="setAdd">加项</van-button>
      <van-button type="primary" color="#1782fa" round bindtap="setUp" wx:if="{{info.status > 0 && info.status < 6}}">补差价</van-button>
    </view>
  </view>
  <van-popup show="{{ upshow }}" round closeable bind:close="onClose">
    <view class="popbox z-padding-32">
      <view class="z-flex z-font-w z-font-30">补差价</view>
      <view class="input_box z-padding-24 z-radius-24 z-margin-tb-32">
        <view class="z-font-30">补差价金额</view>
        <input placeholder="￥0.00" type="digit" value="{{price}}" bindinput="setPrice" />
      </view>
      <view class="z-font-22 text_999">*请与服务人员协商一致后再支付，如有异议请联系平台客服。平台仅对在线支付的项目提供交易保障，请勿线下支付</view>
      <van-divider />
      <view class="popbox_bottom">
        <view class="popbox_bottom_money">
          总计：
          <view class="money z-font-w">￥{{price}}</view>
        </view>
        <van-button bind:tap="uppay" type="primary" color="#1782fa" round>立即支付</van-button>
      </view>
    </view>
  </van-popup>
  <van-popup show="{{ addShow }}" round closeable bind:close="onaddClose">
    <view class="popbox z-padding-32">
      <view class="z-flex z-font-w z-margin-b-24">加项</view>
      <scroll-view scroll-y="true" show-scrollbar="{{false}}" enhanced="true" style="height: 700rpx;"
        bindscrolltolower="pluscd">
        <view class="plus_item z-padding-24 z-radius-24 z-margin-b-24" wx:for="{{addList}}" wx:key="*this">
          <view class="plus_item_left">
            <view class="z-font-w z-font-26">{{item.name}}</view>
            <view class="z-margin-t-16"><text class="money z-font-26">￥{{item.price}}</text><text
                class="z-font-22">/{{item.cost_seconds}}分钟</text></view>
          </view>
          <van-stepper theme='round' min="{{0}}" integer value="{{ item.num }}" data-index="{{index}}"
            bind:change="plusonChange" />
        </view>
      </scroll-view>
      <view class="text_999 z-font-22 z-margin-tb-32">
        *请与服务人员协商一致后再支付，如有异议请联系平台客服。平台仅对在线支付的项目提供交易保障，请勿线下支付
      </view>
      <van-divider />
      <view class="popbox_bottom">
        <view class="popbox_bottom_money">
          总计：
          <view class="money z-font-w">￥{{allprice}}</view>
        </view>
        <van-button type="primary" color="#1782fa" round bindtap="addpay">立即支付</van-button>
      </view>
    </view>
  </van-popup>
</view>
