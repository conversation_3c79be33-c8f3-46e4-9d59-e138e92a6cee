/* service/tentype/tentype.wxss */
.catelist{
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding-top: 0;
}
.scrollbox{
  width: 100vw;
  white-space: nowrap;
  height: 70rpx;
  flex-shrink: 0;
}
.tipsbox{
  display: flex;
  align-items: center;
}
.tipsitem{
  background-color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}
.active{
  display: flex;
  align-items: center;
  font-size: 26rpx;
  background-color: #fff;
  border:  1rpx solid var(--maingreencolor);
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  color: var(--maingreencolor);
  box-sizing: border-box;
}
.tuijian{
  width: 32rpx;
  height: 32rpx;
}

/* 导航栏样式 */
.van-nav-bar {
  background-color: #c8e2ff !important;
  z-index: 999;
}

.van-nav-bar__title {
  font-family: PingFangSC, PingFang SC !important;
  font-weight: 600 !important;
  font-size: 34rpx !important;
  color: #333333 !important;
  text-align: center !important;
  font-style: normal !important;
}

.van-nav-bar__arrow {
    color: #333333 !important;
    font-size: 44rpx !important;
    vertical-align: middle;
}

.van-nav-bar .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

.van-nav-bar__left .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

.main-box{
  flex: 1;
  overflow: hidden;
}
.scroll{
  height: 100%;
  box-sizing: border-box;
}
