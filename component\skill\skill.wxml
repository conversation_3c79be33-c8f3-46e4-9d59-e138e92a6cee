<!--component/skill/skill.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="skillbox_item z-padding-24 z-radius-20 z-margin-b-16" bindtap="skillTap">
  <image src="{{tool.cdn(info.image)}}" class="skillbox_item_img z-radius-20 z-margin-r-16" mode="aspectFill" lazy-load lazy-load-margin="0" />
  <view class="skillinfo">
    <view class="z-font-30 z-font-w">
      {{info.name}}
    </view>
    <view wx:if="{{info.is_rest == 1}}" class="rest z-font-22 ">
    <image src="/static/index/rest.png" class="status-icon"></image>
      <view class="status text_999 z-padding-t-16 z-padding-r-16">休息中</view>
    </view>
    <view wx:elif="{{info.skillTime}}" class="skillbox_item_right z-font-22 ">
      <image src="/static/index/duty.png" class="status-icon"></image>
      
      <view class="status z-padding-t-16 z-padding-r-16">最早可约：{{tool.formatTime(info.skillTime, 'HH:MM')}}</view>
    </view>
    <view class="skill_tips z-margin-tb-16">
      <view class="skill_tips_left">
        <view wx:if="{{info.shopname}}" class="shop_name  z-flex z-padding-lr-8 z-radius-8">
          <image src="../../static/index/shop.png" class="shop_img" mode="" />
          <view class="z-font-18 z-padding-lr-4">{{info.shopname}}</view>
          <van-icon name="arrow" size="10" />
        </view>
        <view class="skill_tips_left_item z-flex z-padding-lr-16 z-radius-4 text_999 z-font-18" wx:if="{{info.age}}">{{info.age+'岁'}}</view>
        <view class="skill_tips_left_item z-flex z-padding-lr-16 z-radius-4 text_999 z-font-18" wx:if="{{info.skillCate}}">{{info.skillCate}}师
        </view>
      </view>
      <view class="skill_tips_right z-font-24 text_999">
        {{tool.toFix(info.distance)}}km
      </view>
    </view>
    <view class="shop">


      <view class="text_999 z-font-24">{{info.commentCount}}评论 <text>{{info.good_comment_percent*100}}%好评</text></view>
      <view class="z-flex-1"></view>
      <view class="choose z-flex z-font-22 z-radius-30" catchtap="appoint">立即预约</view>
    </view>
  </view>
</view>