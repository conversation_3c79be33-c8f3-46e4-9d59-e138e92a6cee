// service/orderdetail/orderdetail.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    show: false,
    addShow: false,
    upshow: false,
    order_id: '',
    info: '',
    safeBottom: `30px`,
    addList: [],
    allprice: '',
    price: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      order_id: options.id
    })
    const high = app.globalData.safeBottom
    this.setData({
      safeBottom: `${high}px`
    })
    
  },
  waitpay(){
    util.skip('/service/paypage/paypage?price='+this.data.info.payprice+'&order_id='+this.data.info.id+'&type=1')
  },
  addpay() {
    let add_ids = [];
    for (let i = 0; i < this.data.addList.length; i++) {
      if (this.data.addList[i].num > 0) {
        add_ids.push(this.data.addList[i].id + '-' + this.data.addList[i].num)
      }
    }
    if (add_ids.length === 0) return util.toast('请选择需要加的项目')
    this.setData({
      addShow: false
    })
    wx.navigateTo({
      url: '/service/paypage/paypage?add_ids=' + add_ids.join(',') + '&order_id=' + this.data.order_id + '&type=2' + '&price=' + this.data.allprice,
    })
  },
  uppay() {
    if (!this.data.price) return util.toast('请输入差价金额')
    this.setData({
      upshow: false
    })
    wx.navigateTo({
      url: '/service/paypage/paypage?price=' + this.data.price + '&order_id=' + this.data.order_id + '&type=3',
    })
  },
  setAdd() {
    http.get('goods/goodsaddlist', {
      id: this.data.info.detail.goods_id
    }).then(res => {
      if (res.data.length === 0) {
        util.toast('该项目暂无可加的项目')
      } else {
        let arr = res.data.map(value => {
          value.num = 0
          return value
        })
        this.setData({
          addShow: true,
          addList: arr
        })
      }

    })
  },
  setUp() {
    this.setData({
      upshow: true
    })
  },
  onaddClose() {
    this.setData({
      addShow: false,
      allprice: 0
    })
  },
  onClose() {
    this.setData({
      upshow: false
    })
  },
  setPrice(e) {
    this.setData({
      price: e.detail.value
    })
  },
  plusonChange(event) {
    let index = event.currentTarget.dataset.index
    this.data.addList[index].num = event.detail
    let allprice = 0;
    for (let i = 0; i < this.data.addList.length; i++) {
      allprice += this.data.addList[i].num * this.data.addList[i].price
    }
    this.setData({
      addList: this.data.addList,
      allprice: allprice
    })
  },
  getInfo() {
    http.get('order/orderInfo', {
      id: this.data.order_id
    }).then(res => {
      this.setData({
        info: res.data
      })

    })
  },
  //跳转举报
  toreport() {
    util.skip('/service/report/report?order_id=' + this.data.order_id, {
      'applyReport': () => {
        this.getInfo()
      }
    })
  },
  //申请退款
  toapplyrefund() {
    util.skip('/service/applyrefund/applyrefund?order_id=' + this.data.order_id, {
      'applyRefund': () => {
        this.getInfo()
      }
    })
  },
  clickshow() {
    this.setData({
      show: this.data.show == true ? false : true
    })
  },
  openLoaction(){
    wx.openLocation({
      latitude : Number(this.data.info.shopInfo.lat),
      longitude : Number(this.data.info.shopInfo.lng),
      name: this.data.info.shopInfo.address,
      scale: 18
    })
  },
  call(event) {
    wx.makePhoneCall({
      phoneNumber: event.currentTarget.dataset.content,
    })
  },
  copyclick(event) {
    wx.setClipboardData({
      data: event.currentTarget.dataset.content,
      success(res) {
        util.toast('已复制')
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getInfo()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})