// service/tentype/tentype.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    cate: [{id: '', label: '推荐'}],
    list:[],
    finish: false,
    loading: false,
    page: 1,
    category_id: '',
    two_category_id: '',
    address: '',
    title: '项目列表'
  },
  serviceTap(e){
    util.skip('/service/servedetail/servedetail?id='+e.detail.id)
  },
  choosetype(e){
    this.setData({
      two_category_id:e.currentTarget.dataset.id
    })
    this.reload()
  },
  reload(){
    this.setData({
      list: [],
      page: 1,
      finish: false
    })
    this.getList()
  },
  more(){
    this.setData({
      page: ++this.data.page
    })
    this.getList()
  },
  getList(){
    if(this.data.finish){
      return 
    }
    if(this.data.loading){
      return 
    }
    this.setData({
      loading: true
    })
    let data = {
      page: this.data.page,
      city: this.data.address.city
    }
    if(this.data.category_id){
      data['category_id'] =  this.data.category_id
    }
    if(this.data.two_category_id){
      data['two_category_id'] =  this.data.two_category_id
    }
    http.get('goods/getlist', data).then(res => {
      if(res.data.length === 0){
        this.setData({
          finish: true
        })
      }
      let arr = this.data.list.concat(res.data)
      this.setData({
        list: arr
      })
      
    }).finally(res => {
      this.setData({
        loading: false
      })
    })
  },
  getInfo(){
    http.get('category/getson', {
      id: this.data.category_id
    }).then(res => {
      let arr = this.data.cate.concat(res.data)
      this.setData({
        cate: arr
      })
      
    })
  },
  onLoadAddress(option){
    this.setData({
      address: app.globalData.address
    })
    this.reload()
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      category_id: options.category_id || '',
      two_category_id: options.two_category_id || '',
      title: options.name || '项目列表'
    })
    if(options.category_id){
      this.getInfo()
    }
  },
  onClickLeft() {
    wx.navigateBack()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})