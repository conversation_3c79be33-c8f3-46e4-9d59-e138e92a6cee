// service/addnewaddress/addnewaddress.js
import http from "../../utils/http"
import util from "../../utils/util"
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    type: 'add',
    name: '',
    sex: 1,
    state: 0,
    mobile: '',
    province: '',
    city: '',
    district: '',
    area: '',
    address: '',
    lng: '',
    lat: '',
    info: '',
    safeBottom: `30px`
  },
  save() {
    if (this.data.lng === '') return util.toast('请选择地址')
    if (this.data.address.trim() === '') return util.toast('请输入门牌号信息')
    if (this.data.name.trim() === '') return util.toast('请输入联系人')
    if (this.data.mobile.trim() === '') return util.toast('请输入联系人手机号')
    if(!/^1\d{10}$/.test(this.data.mobile)) return util.toast('请输入正确的联系人手机号')
    let data = {
      type: this.data.type,
      name: this.data.name,
      sex: this.data.sex,
      state: this.data.state,
      mobile: this.data.mobile,
      province: this.data.province,
      city: this.data.city,
      district: this.data.district,
      area: this.data.area,
      address: this.data.address,
      lng: this.data.lng,
      lat: this.data.lat,
    }
    if (this.data.type === 'edit') {
      data['id'] = this.data.id
    }
    http.post('/address/handleaddress', data,true).then(res => {
      util.toast(res.msg)
      const eventChannel = this.getOpenerEventChannel()
      eventChannel.emit('saveAddress');
      util.back();
    })
  },
  chooseAddress() {
    http.chooseLocation(this.data.lat,this.data.lng).then(res => {
      this.setData({
        province: res.province,
        city: res.city,
        district: res.district,
        area: res.name,
        lng: res.lng,
        lat: res.lat,
        info: res.city + ' ' + res.name
      })
    })
  },
  setAddress(e) {
    this.setData({
      address: e.detail.value
    })
  },
  setMobile(e) {
    this.setData({
      mobile: e.detail.value
    })
  },
  setName(e) {
    this.setData({
      name: e.detail.value
    })
  },
  choosesex() {
    this.setData({
      sex: this.data.sex == 1 ? 0 : 1
    })
  },
  choosedefault() {
    this.setData({
      state: this.data.state == 1 ? 0 : 1
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const high = app.globalData.safeBottom
    this.setData({
      safeBottom: `${high}px`
    })
    if (options.type) {
      const eventChannel = this.getOpenerEventChannel()

      eventChannel.on('editInfo',  (data) =>{
        this.setData({
          type: 'edit',
          id: data.id,
          name: data.name,
          sex: data.sex,
          state: data.state,
          mobile: data.mobile,
          province: data.province,
          city: data.city,
          district: data.district,
          area: data.area,
          address: data.address,
          lng: data.lng,
          lat: data.lat,
          info: data.city + ' ' + data.area
        })
      })

      
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})