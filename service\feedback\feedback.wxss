/* service/feedback/feedback.wxss */
page{
  background-color: #fff;
}
.feedback_box{
  background-color: #F5F7F9;
  box-sizing: border-box;
}
.inputbox{
  background-color: #F5F7F9;
  box-sizing: border-box;
}
.content{
  width: 100%;
}

.box{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.images-box{
  position: relative;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}
.images-box:nth-of-type(4n){
  margin-right: 0;
}
.del{
  position: absolute;
  width: 26rpx;
  height: 26rpx;
  right: 0;
  top: -13rpx;
  z-index: 10;
}

.icon{
  width: 140rpx;
  height: 140rpx;
}

/* 导航栏样式 */
.van-nav-bar {
  background-color: #c8e2ff !important;
  z-index: 999;
}

.van-nav-bar__title {
  font-family: PingFangSC, PingFang SC !important;
  font-weight: 600 !important;
  font-size: 34rpx !important;
  color: #333333 !important;
  text-align: center !important;
  font-style: normal !important;
}

.van-nav-bar__arrow {
    color: #333333 !important;
    font-size: 44rpx !important;
    vertical-align: middle;
}

.van-nav-bar .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

.van-nav-bar__left .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}
