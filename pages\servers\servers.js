// pages/servers/servers.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    address:'',
    menuH: '32px',
    safeTop: `40px`,
    active: 0,
    cate: [{id: '', name: '全部'}],
    list:[],
    skill_cate_id: '',
    finish: false,
    loading: false,
    page: 1,
    name: ''
  },
  selectAddress(){
    util.skip('/service/selectAddress/selectAddress?city='+this.data.address.city+'&name='+this.data.address.name)
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      safeTop: `${app.globalData.safeTop}px`,
      menuH: `${app.globalData.menuH}px`,
    })
    this.getInfo()
  },
  nameChange(e) {
    this.setData({
      name: e.detail,
    });
  },
  onSearch(){
    if(this.data.value === '')  return util.toast('请输入搜索内容')
    this.reload()
  },
  onShowAddress(option){
    this.setData({
      address: app.globalData.address
    })
    this.reload()
  },
  reload(){
    this.setData({
      list: [],
      page: 1,
      finish: false
    })
    this.getList()
  },

  getList(){
    if(this.data.finish){
      return
    }
    if(this.data.loading){
      return
    }
    this.setData({
      loading: true
    })
    http.get('skill/searchskill', {
      city: this.data.address.city,
      lat: this.data.address.lat,
      lng: this.data.address.lng,
      page: this.data.page,
      skill_cate_id: this.data.skill_cate_id,
      name: this.data.name
    }).then(res => {
      if(res.data.length === 0){
        this.setData({
          finish: true
        })
      }
      let arr = this.data.list.concat(res.data)
      this.setData({
        list: arr
      })

    }).finally(res => {
      this.setData({
        loading: false
      })
    })
  },
  getInfo(){
    http.post('skillcate/getlist', '', true).then(res => {

      let arr = this.data.cate.concat(res.data)
      this.setData({
        cate: arr
      })
    })
  },
  onChange(e){
    this.setData({
      skill_cate_id: this.data.cate[e.detail.index].id
    })
    this.reload()
  },
  lowerthreshold(){
    this.setData({
      page: ++this.data.page
    })
    this.getList()
  },
  //跳转服务者详情
  skillTap(e){
    util.skip('/service/skilldetail/skilldetail?skill_id='+e.detail.id)
  },
  choosetype(e){
    this.setData({
      current:e.currentTarget.dataset.id
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {}
  }
})
