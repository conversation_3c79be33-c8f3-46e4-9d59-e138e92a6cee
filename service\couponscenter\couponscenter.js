// service/couponscenter/couponscenter.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    safeBottom: `30px`,
    list:[],
    finish: false,
    loading: false,
    page: 1,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  code(){
    wx.navigateTo({
      url: '/service/code/code',
    })
  },
  couponTap(e){
    if(e.detail.type == 1){
      http.get('coupon/receive', {
        type: 0,
        coupon_id: e.detail.info.id
      }).then(res => {
        util.toast(res.msg)
        this.reload()
      })
    }else if(e.detail.type == 2){
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
    
  },
  reload(){
    this.setData({
      list: [],
      page: 1,
      finish: false
    })
    this.getList()
  },
  more(){
    this.setData({
      page: ++this.data.page
    })
    this.getList()
  },
  getList(){
    if(this.data.finish){
      return 
    }
    if(this.data.loading){
      return 
    }
    this.setData({
      loading: true
    })
    http.get('coupon/getlist', {
      page: this.data.page,
    }).then(res => {
      if(res.data.length === 0){
        this.setData({
          finish: true
        })
      }
      let arr = this.data.list.concat(res.data)
      this.setData({
        list: arr
      })
      
    }).finally(res => {
      this.setData({
        loading: false
      })
    })
  },
  onLoad(options) {
    const high = app.globalData.safeBottom
    this.setData({
      safeBottom: `${high}px`
    })
    this.reload()
  },

  
})