// service/recharger/recharger.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    money:['100','200','300'],
    price: 100,
    menuH: '32px',
    safeTop: `40px`,
    paytype: 0,
    agree: true,
    info: '',
    packageList:[],
    price_id:''
  },
  // 获取充值套餐
  getSetMeal(){
    http.post('recharge/recharge').then(res => {
      this.setData({
        packageList:res.data.packageList
      })
    })
  },

  fanhui(){
    util.back()
  },
  clickread() {
    this.setData({
      agree: !this.data.agree
    })
  },
  toDetail(){
    util.skip('/service/balanceLog/balanceLog')
  },
  cut(e){
    this.setData({
      // price:e.currentTarget.dataset.current
      price_id:e.currentTarget.dataset.current
    })
  },
  setPrice(e){
    this.setData({
      price: e.detail.value,
    })
  },
  infoTap(e){
    let info = e.currentTarget.dataset.info
    let name = e.currentTarget.dataset.name
    let id = app.globalData.config[info]
    util.skip('/service/info/info?id='+id+'&name='+name)
  },
  pay(){
    if(!this.data.agree) return util.toast('请先阅读并同意《用户充值协议》')
    http.post('recharge/recharge', {
      type: 'recharge',
      paytype: this.data.paytype,
      recharge_package_id:this.data.price_id
    }).then(res => {
      wx.requestPayment({
        timeStamp: res.data.timeStamp,
        nonceStr: res.data.nonceStr,
        package: res.data.package,
        signType: res.data.signType,
        paySign: res.data.paySign,
        success: (res) => {
          util.toast('支付成功')
          this.getInfo()
        },
        fail: (res) => {
          util.toast('支付失败')
        }
      })
    })
  },
  getInfo(){
    http.post('index/userindex', '').then(res => {
      this.setData({
        info: res.data
      })
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      safeTop: `${app.globalData.safeTop}px`,
      menuH: `${app.globalData.menuH}px`,
    })
    this.getInfo()
    this.getSetMeal()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})