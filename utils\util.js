import config from './config'

const formatTime = (time, format = 'yyyy-mm-dd HH:MM') => {
  let date;
  if(time.toString().length == 13){
    date = new Date(time);
  }else{
    date = new Date(time * 1000);
  }
  const year = date.getFullYear()
  const month = formatNumber(date.getMonth() + 1)
  const day = formatNumber(date.getDate())
  const hour = formatNumber(date.getHours())
  const minute = formatNumber(date.getMinutes())
  const second = formatNumber(date.getSeconds())
  
  format = format.replace('yyyy',year)
  format = format.replace('mm',month)
  format = format.replace('dd',day)
  format = format.replace('HH',hour)
  format = format.replace('MM',minute)
  format = format.replace('ss',second)

  return format
} 

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

const cdn = (url) => {
	if (!url) {
		return ''
	}
	if (typeof url === 'string') {
		if ((/^(http|https):\/\/.+/.test(url))) {
			return url
		} else{
			return config.cdn + url
		}
	} else {
		let arr = []
		for (let i = 0; i < url.length; i++) {
      if ((/^(http|https):\/\/.+/.test(url[i]))) {
        arr.push(url[i])
      } else {
        arr.push(config.cdn + url[i])
      }
		}
		return arr
	}
}
const cdn2 = (url) => {
	if (!url) {
		return ''
	}
	if (typeof url === 'string') {
		if ((/^(http|https):\/\/.+/.test(url))) {
			return url
		} else{
			return config.domain + url
		}
	} else {
		let arr = []
		for (let i = 0; i < url.length; i++) {
      if ((/^(http|https):\/\/.+/.test(url[i]))) {
        arr.push(url[i])
      } else {
        arr.push(config.domain + url[i])
      }
		}
		return arr
	}
}

const skip = (url, events = {}, success)=>{
  wx.navigateTo({
    url,
    events,
    success
  })
}

const authSkip = (url, events = {})=>{
  if(wx.getStorageSync('token')){
    // 定义tabbar页面列表
    const tabbarPages = [
      '/pages/index/index',
      '/pages/classify/classify', 
      '/pages/myorder/myorder',
      '/pages/my/my'
    ];
    
    // 提取URL路径（去掉参数）
    const urlPath = url.split('?')[0];
    
    // 判断是否为tabbar页面
    if(tabbarPages.includes(urlPath)){
      wx.switchTab({
        url: urlPath
      })
    }else{
      wx.navigateTo({
        url,
        events
      })
    }
  }else{
    wx.navigateTo({
      url: '/service/login/login',
      events
    })
  }
}

const back = (delta = 1) => {
  wx.navigateBack({
    delta: delta
  })
}

const toast = (msg, time = 2000) =>{
  wx.showToast({
    title: msg,
    icon: 'none',
    duration: time
  })
}



function setWatcher(page) {
  let data = page.data;
  let watch = page.watch;
  Object.keys(watch).forEach(v => {
      let key = v.split('.'); 
      let nowData = data; 
      for (let i = 0; i < key.length - 1; i++) { 
          nowData = nowData[key[i]]; 
      }
      let lastKey = key[key.length - 1];
      let watchFun = watch[v].handler || watch[v]; 
      let deep = watch[v].deep; 
      observe(nowData, lastKey, watchFun, deep, page); 
  })
}

function observe(obj, key, watchFun, deep, page) {
  var val = obj[key];
  if (deep && val != null && typeof val === 'object') {
      Object.keys(val).forEach(childKey => { 
          observe(val, childKey, watchFun, deep, page); 
      })
  }
  Object.defineProperty(obj, key, {
      configurable: true,
      enumerable: true,
      set: function(newVal) {
          watchFun.call(page, newVal, val); 
          val = newVal;
          if (deep) { 
              observe(obj, key, watchFun, deep, page);
          }
      },
      get: function() {
          return val;
      }
  })
}

const previewImage = (arr,index=0)=>{
  wx.previewImage({
    urls: cdn(arr),
    current: cdn(arr)[index]
  })
}

module.exports = {
  formatTime : formatTime,
  cdn : cdn,
  cdn2 : cdn2,
  skip : skip,
  authSkip: authSkip,
  back : back,
  toast : toast,
  setWatcher: setWatcher,
  previewImage: previewImage
}
