<!--pagesA/servedetail/servedetail.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="servedetail" wx:if="info">
  <van-nav-bar
    title="服务详情"
    left-arrow
    bind:click-left="onClickLeft"
    custom-style="background-color: #c8e2ff;"
    title-style="width: 136rpx; height: 36rpx; font-family: PingFangSC, PingFang SC; font-weight: 600; font-size: 34rpx; color: #333333; line-height: 36rpx; text-align: right; font-style: normal;"
  />
  <view class="main-box">
    <view class="swiper-box">
      <swiper class="swiperimg" bindchange="bindchange">
        <swiper-item wx:for="{{tool.split(info.images || '',',')}}">
          <image src="{{tool.cdn(item)}}" class="swiperimg" mode="aspectFill" bindtap="previewImages"
            data-index="{{index}}" />
        </swiper-item>
      </swiper>
      <view class="tips z-padding-tb-8 z-padding-lr-16 z-radius-32 z-font-24">
        {{current+1}}/{{tool.split(info.images || '',',').length}}</view>
    </view>
    <view class="activeone z-padding-32">
      <view class="next_one z-radius-24  z-padding-b-32">
        <view class="next_one_top">
          <view class="next_one_top_text z-padding-16 z-font-24">
            最快{{info.response_hour}}小时上门
          </view>
        </view>
        <view class="money z-font-w z-padding-lr-32" style="margin-top: -30rpx;">
          <view class="z-font-30 z-margin-t-8">￥</view>
          <view class="z-font-40 z-font-w">{{info.price}}</view>
        </view>
        <view class="z-padding-lr-32 z-padding-t-24 z-padding-b-16 name">
          <view class="z-font-w z-font-32"> {{info.name}}</view>
          <view class="text_999 z-font-24">已售{{info.salenums}}</view>
        </view>
        <view class="z-padding-lr-32  text_999" wx:if="{{info}}">
          <block wx:for="{{tool.split(info.tag_name,'|')}}">
            <text class="text_999 z-font-22">{{item}}</text>
            <text class="lines z-margin-lr-8" wx:if="{{index+1 < tool.split(info.tag_name,'|').length}}"></text>
          </block>
        </view>
      </view> 
     
      <view class="content-box z-margin-t-24 z-radius-20">
        <view class="z-flex z-margin-b-24">
          <view class="z-font-30 content-title">服务流程</view>
        </view>
        <block wx:for="{{tool.split(info.flow_path_images || '',',')}}" wx:key="*this">
          <image src="{{tool.cdn(item)}}" class="info-img" mode="widthFix"></image>
        </block>
      </view>
      <view class="comment_box" wx:if="{{commentInfo.totalNum>0}}">
        <view class="comment_box_top z-margin-t-32 z-margin-b-24">
          <view class="z-font-w">评价({{commentInfo.totalNum}})</view>
          <view class="text_999 z-font-24" bindtap="comment">好评率{{commentInfo.goodPercent*100}}%
            <van-icon name="arrow" />
          </view>
        </view>
        <block wx:for="{{list}}" wx:key="item.id" wx:if="{{index < 2}}">
          <comment info="{{item}}"></comment>
        </block>
      </view>
      <view class="content-box  z-margin-t-24 z-radius-20">
        <view class="z-flex z-margin-b-24">
          <view class="z-font-30 content-title">收费标准</view>
        </view>
        <block wx:for="{{tool.split(info.illustrate_images || '',',')}}" wx:key="*this">
          <image src="{{tool.cdn(item)}}" class="info-img" mode="widthFix" bindtap="previewIll" data-index="{{index}}">
          </image>
        </block>
      </view>



    </view>


  </view>
  <view class="bottom" style="padding-bottom:{{safeBottom}}">
    <view class="bottom_content z-padding-lr-32 z-padding-t-32">
      <view class="bottom_content_left z-margin-r-32">
        <image src="/static/service/kefu.png" class="bottom_content_img" mode="" />
        <view class="z-font-22 text_999">客服</view>
        <button type="primary" open-type="contact" class="contact"></button>
      </view>
      <view class="bottom_content_left z-margin-r-32" bindtap="collect">
        <image src="/static/service/collected.png" wx:if="{{info.followState}}" class="bottom_content_img" mode="" />
        <image src="/static/service/collect.png" wx:else class="bottom_content_img" mode="" />
        <view class="z-font-22 text_999" wx:if="{{info.followState}}">已收藏</view>
        <view class="z-font-22 text_999" wx:else>收藏</view>
      </view>
      <view class="bottom_content_left z-margin-r-32" bindtap="share">
        <image src="/static/service/share.png" class="bottom_content_img" mode="" />
        <view class="z-font-22 text_999">分享</view>
      </view>
      <view class="vbutton">
        <view class="book-button" bindtap="openshow">立即预约</view>
      </view>

    </view>
  </view>
  <van-popup position="bottom" show="{{ show }}" z-index='9999' bind:close="onClose" safe-area-inset-bottom="{{false}}">
    <view class="popcontent z-padding-lr-32 z-padding-t-32 z-radius-tl-20">
      <view class="popcontent_top">
        <image src="{{tool.cdn(info.image)}}" class="popcontent_img z-margin-r-32 z-flex-0" mode="aspectFill" />
        <view class="popcontent_top_text">
          <view>{{info.name}}</view>

          <view class="z-padding-r-32  text_999" wx:if="{{info}}">
            <block wx:for="{{tool.split(info.tag_name,'|')}}">
              <text class="text_999 z-font-22">{{item}}</text>
              <text class="lines z-margin-lr-8" wx:if="{{index+1 < tool.split(info.tag_name,'|').length}}"></text>
            </block>
          </view>
        </view>
        <view class="money">￥<view class="z-font-w">{{price}}</view></view>
      </view>
      <view class="specification z-margin-tb-32">
        <view class="spu">
          <block wx:for="{{info.spu}}" wx:key="id">
            <view class="text_999 z-font-24">{{item.name}}</view>
          <view class="project_box z-margin-tb-24">
            <view class="z-flex-c spu-box">
              <view class="z-flex z-margin-b-16 z-margin-r-16 spu-item {{item.select==val?'active':''}}" wx:for="{{item.skudetail}}" wx:for-item="val"  wx:for-index="i" bindtap="selectSpu" data-index="{{index}}" data-item="{{val}}">{{val}}</view>
            </view>
          </view>
          </block>
        </view>
        <!-- <view class="text_999 z-font-24">选择项目</view>
        <view class="project_box z-margin-tb-24">
          <view bind:tap="chooseSku" data-id="{{item.id}}" data-price="{{item.price}}"
            class="{{item.id==goods_sku_id?'project_one':'project_item'}} z-flex-c z-margin-b-24" wx:for="{{info.sku}}"
            wx:key="id">
            <view class="z-flex-1">
              <view> {{info.name}}</view>
              <view class="z-margin-t-16 z-font-24 text_999"> {{tool.join(tool.split(item.name,','),'/')}}</view>
            </view>
            <view class="money">￥<view class="z-font-w">{{item.price}}</view>
            </view>

          </view>
        </view> -->
        <view class="step">
          <view class="text_999 z-font-24">数量</view>
          <view class="z-flex z-margin-l-40">
            <image src="../../static/service/sub.png" class="icon" bindtap="sub"></image>
            <view class="z-margin-lr-24 z-font-32">{{num}}</view>
            <image src="../../static/service/add.png" class="icon" bindtap="add"></image>
          </view>
        </view>
      </view>
      <view style="padding-bottom: {{safeBottom}}">
        <van-button type="primary" color="#1782fa" round bind:tap="makeorder" size="large">立即预约</van-button>
      </view>
    </view>
  </van-popup>
</view>