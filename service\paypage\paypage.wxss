/* service/paypage/paypage.wxss */
page {
  background-color: #fff;
}

.paypage{
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.main-box{
  flex: 1;
  overflow-y: auto;
}
.money {
  display: flex;
  color: var(--mainmoneycolor);
  align-items: center;
  margin-top: 100rpx;
}

.money_text {
  font-size: 78rpx;
}



.pay_box {
  background-color: #F5F7F9;
  box-sizing: border-box;
  margin: 32prx auto;
  width: 686rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pay_box_left {
  display: flex;
  align-items: center;
}

.wx_img {
  width: 40rpx;
  height: 40rpx;
}
.choose_img{
    width: 32rpx;
    height: 32rpx;
}