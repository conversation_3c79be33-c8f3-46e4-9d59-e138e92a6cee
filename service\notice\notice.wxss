/* pages/training/training.wxss */
page {
  background-color: #EDF1F4;
}
.notice{
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.scroll-box{
  height: 100%;
  box-sizing: border-box;
}
.list{
  margin-bottom: 24rpx;
}
.list:last-of-type{
  margin-bottom: 0;
}

.item-text {
  flex: 1;
  line-height: 48rpx;
}
.title{
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  height: 90rpx;
}

.item-img {
  width: 200rpx;
  height: 150rpx;
  opacity: 1;
}
.each-item {
  background-color: #FFF;
}

.theme-color{
  color: #1782FA;
}