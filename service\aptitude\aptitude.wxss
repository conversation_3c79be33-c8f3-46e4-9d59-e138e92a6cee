/* service/aptitude/aptitude.wxss */
page{
  background-color: #fff;
}
.aptituteimg{
  width: 204rpx;
  height: 194rpx;
  position: absolute;
  top: 1rpx;
}
.top{
  display: flex;
  align-items: center;
  justify-content: center;
  height: 190rpx;
}
.rate{
  color: var(--mainmoneycolor);
  position: relative;
  right: 5rpx;
}
.shopname{
  background-color: #F5F7F9;
  width: 686rpx;
  box-sizing: border-box;
  margin: 50rpx auto;
  display: flex;
  justify-content: space-between;
}
.sure{
  background-color: #fff;
  height: 56rpx;
}
.licence{
  margin: 32rpx auto;
}