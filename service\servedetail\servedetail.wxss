/* pagesA/servedetail/servedetail.wxss */
.servedetail{
  background-color: #fff;
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.main-box{
  flex: 1;
  overflow-y: auto;
}
.swiper-box{
  position: relative;
}
.tabs_box::-webkit-scrollbar{
  display: none;
}
.swiperimg{
  width: 750rpx;
  height: 686rpx;
}
.activeone{
  box-sizing: border-box;
}
.tips{
  position: absolute;
  right: 50rpx;
  color: #fff;
  background: rgba(0,0,0,0.5);
  top: 600rpx;
}
.next_one{
  box-sizing: border-box;
  margin: 0 auto;
  background-color: #fff;
  position: relative;
}
.next_one_top{
  display: flex;
  justify-content: flex-end;
}
.next_one_top_text{
  background: rgba(98,203,129,0.1);
  color: #1782FA;
  border-radius: 0 20rpx 0 20rpx;
}
.money{
  display: flex;
  align-items: center;
  color:var(--mainmoneycolor);
}
.name{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.lines{
  display: inline-block;
  width: 1rpx;
  height: 18rpx;
  background-color: #EDEEF1;
}

.next_two{
  background: linear-gradient(180deg, #F0FAF3 0%, #FFFFFF 100%);
  width: 90vw;
  box-sizing: border-box;
  margin: 0 auto;
}
.next_two_top{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.fourimg{
 display: flex;
 justify-content: space-between;
}
.fourimg_item{
  width: 64rpx;
  height: 64rpx;
}
.van-steps{
  background-color: rgba(0, 0, 0, 0) !important;
  margin-top: -20rpx;
}
.fourtext{
  display: flex;
  justify-content: space-between;
}
.fourtext_item{
  display: flex;
  flex-direction: column;
  align-items: center;
}
.comment_box{
  width: 90vw;
  margin: 0 auto;
  box-sizing: border-box;
}
.comment_box_top{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.comment_item{
  background-color: #fff;
  box-sizing: border-box;
}
.comment_item_top{
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.comment_item_top_left{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.comment_item_top_left_text{
  display: flex;
  flex-direction: column;
}

.comment_item_img{
  width: 66rpx;
  height: 66rpx;
  border-radius: 50%;
}
.scroll_box{
  width: 600rpx;
  white-space: nowrap;
}
.scroll_box_imgbox{
  display: flex;
}
.scroll_box_img{
  width: 190rpx;
  height: 190rpx;
}
.scroll_box_img:nth-child(n+2){
  margin-left: 30rpx;
}
.content-box{
  background: #fff;
}
.line{
  width: 6rpx;
  height: 28rpx;
  background: linear-gradient(#1782FA, #E3FDCE);
}
.bottom{
  background-color: #fff;
  width: 100vw;
}
.bottom_content{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.bottom_content_img{
  width: 48rpx;
  height: 48rpx;
}
.bottom_content_left{
  align-items: center;
  display: flex;
  flex-direction: column;
}
.vbutton{
}
.popcontent{
  position: relative;
  z-index: 23 !important;
}

.popcontent_top{
  display: flex;
  align-items: center;
}
.project_box{
  max-height: 600rpx;
  overflow-y: auto;
}
.project_box::-webkit-scrollbar{
  display: none;
}
.popcontent_img{
  width: 114rpx;
  height: 114rpx;
}
.popcontent_top_text{
  display: flex;
  flex-direction: column;
  height: 114rpx;
  justify-content: space-around;
  flex: 1;
}
.money{
  display: flex;
  color:var(--mainmoneycolor);
}
.specification_box{
  display: flex;
  flex-wrap: wrap;
}


.specification_item{
  padding: 10rpx 40rpx;
  background-color: #F5F7F9;
  border-radius: 15rpx;
}

.one{
  border: 1rpx solid #1782FA;
  background-color: #F0FAF3;
  border-radius: 15rpx;
  color: #1782FA;
  padding: 10rpx 40rpx;
}
.project_item{
  height: 120rpx;
  box-sizing: border-box;
  padding: 0 40rpx;
  background-color: #F5F7F9;
  border-radius: 15rpx;
  
}
.project_one{
  height: 120rpx;
  border: 1rpx solid #1782FA;
  background-color: #F0FAF3;
  border-radius: 15rpx;
  color: #1782FA;
  padding: 0 40rpx;
}

.spu{
  max-height: 600rpx;
  overflow-y: auto;
}

.spu-box{
  flex-wrap: wrap;
}

.spu-item{
  height: 64rpx;
  padding: 0 40rpx;
  background: #F5F7F9;
  box-sizing: border-box;
  color: #1C274C;
  border-radius: 10rpx;
  font-size: 22rpx;
}
.active{
  border: 1rpx solid #1782FA;
  background-color: #F0FAF3;
  color: #1782FA;
}


.info-img{
  width: 100%;
}
.step{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.icon{
  width: 32rpx;
  height: 32rpx;
}


.levelBox{
  background: #1C274C;
  height: 70rpx;
  margin-top: -30rpx;
   padding-top: 30rpx;
}
.level-icon{
  width: 30rpx;
  height: 30rpx;
}
.level-info{
  color: #F7C566;
}

.bottom_content_img{
  width: 48rpx;
  height: 48rpx;
}
.bottom_content_left{
  align-items: center;
  display: flex;
  flex-direction: column;
  position: relative;
}
.contact{
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}

.book-button{
  width: 418rpx;
  height: 76rpx;
  background: #1782FA;
  border-radius: 38rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
}

/* 弹窗中的立即预约按钮样式 */
.van-button--primary {
  background-color: #1682fb !important;
  border-color: #1682fb !important;
  border-radius: 50rpx!important;
}

.content-title{
 
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  text-align: left;
  font-style: normal;
}

/* 导航栏样式 */
.van-nav-bar {
  background-color: #c8e2ff !important;
  z-index: 999;
}

.van-nav-bar__title {
  font-family: PingFangSC, PingFang SC !important;
  font-weight: 600 !important;
  font-size: 34rpx !important;
  color: #333333 !important;
  text-align: center !important;
  font-style: normal !important;
}

.van-nav-bar__arrow {
    color: #333333 !important;
    font-size: 44rpx !important;
    vertical-align: middle;
}

.van-nav-bar .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

.van-nav-bar__left .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}
