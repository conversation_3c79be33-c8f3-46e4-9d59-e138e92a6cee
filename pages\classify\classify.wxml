<!--pages/classify/classify.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="classify">
  <view class="header-section">
    <view class="logo-area">
      <image src="https://service.infooi.cn/uploads/20250727/5ede84868f5fdba04df2b57927d9fd62.png" class="logo-image"></image>
    </view>
    <view class="search-bar">
      <view class="search-content">
        <image src="https://service.infooi.cn/uploads/20250727/a158c6577522b754633c5d4d6dab4615.png" class="search-icon"></image>
        <text lines="1" class="search-placeholder">家电不好使？即可预约上门</text>
      </view>
    </view>
  </view>
  <view class="classify_box ">
    <!-- 左盒子 -->
    <scroll-view class="classify_left" scroll-y show-scrollbar="{{false}}" enhanced="true">
      <view class="z-font-30 label hidden {{active == index ? 'active text_333' : 'text_999'}} {{active + 1 == index ? 'below-active' : ''}}" bindtap="change" data-index="{{index}}" wx:for="{{cate}}" wx:key="index">
        {{item.label}}
      </view>
    </scroll-view> 


    <!-- 右盒子 -->
    <view class="classify_right ">
      <!-- <view class="classify_right_title z-padding-30 z-font-w">
        {{cate[active].label}}
      </view> -->
      <view class="classify_right_con">
        <scroll-view scroll-y show-scrollbar="{{false}}" enhanced="true" class="classify_right_scroll" bindscrolltolower="lowerthreshold">
          <view class="classify_right_box">
            <view class="classify_right_item z-margin-l-24 z-margin-b-24 " wx:for="{{cate[active].children}}" wx:key="cate[active].id" data-item="{{item}}" bindtap="cateTap">
              <image src="{{tool.cdn(item.image)}}" class="classify_right_box_img z-radius-16" mode="aspectFill" />
              <view class="z-margin-t-16 z-font-22">{{item.label}}</view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</view>