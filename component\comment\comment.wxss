/* component/comment/comment.wxss */
@import '../../common.wxss';
.comment{
  background-color: #fff;
  box-sizing: border-box;
}
.comment_item_top{
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.comment_item_top_left{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.comment_item_top_left_text{
  display: flex;
  flex-direction: column;
}

.comment_item_img{
  width: 66rpx;
  height: 66rpx;
  border-radius: 50%;
}
.scroll_box{
  flex-wrap: wrap;
}
.scroll_box_imgbox{
  display: flex;
}
.scroll_box_img{
  width: 190rpx;
  height: 190rpx;
  margin-right: 24rpx;
  margin-bottom: 24rpx;
}
.scroll_box_img:nth-of-type(3n){
  margin-right: 0;
}

.lable-box{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.label{
  height: 56rpx;
  background: #F5F7F9;
  color: #647390;
}