const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    city:[],
    originalCity:[],
    list:[],
    realAddress: '',
    name: '',
    times:''
  },
  selectCity(e){
    let city = e.currentTarget.dataset.city
    const eventChannel = this.getOpenerEventChannel()
    eventChannel.emit('selectCity',city);
    util.back()
  },
  getList(){
    http.get('user/getarea').then(res => {
      let city=[];
      for(let i=65; i<= 90; i++){
        let arr = res.data.areaList.filter(value => {
          // if(value.first){
          //   return value.first == String.fromCharCode(i)
          // }else {
          //   return value.pinyin.substr(0,1).toUpperCase() == String.fromCharCode(i)
          // }
          return value.pinyin.substr(0,1).toUpperCase() == String.fromCharCode(i)
          
        })
        city.push({
          anchor: String.fromCharCode(i),
          area: arr
        })
      }
      this.setData({
        list: res.data.openCity,
        city: city,
        originalCity:city
      })
    })
  },
  searchCity(keyword) {
    console.log('keywordkeywordkeyword',keyword)
    keyword = keyword.toLowerCase(); // 将搜索词转换为小写，以便忽略大小写

    // 使用 flatMap 方法来返回符合条件的城市数据
    let results = this.data.originalCity.flatMap(city => {
      // 使用 filter 方法过滤出包含指定关键词的城市数据
      let filteredAreas = city.area.filter(area => area.name.toLowerCase().includes(keyword));

      // 如果有符合条件的数据，则返回包含这些数据的格式
      if (filteredAreas.length > 0) {
          return { anchor: city.anchor, area: filteredAreas };
      } else {
          return []; // 如果没有符合条件的数据，则返回空数组
      }
    });

    return results;
  },
  setName(e){
    this.setData({
      name: e.detail.value
    })
    clearTimeout(this.data.times)
    this.data.times = setTimeout(()=>{
      console.log(this.searchCity(e.detail.value))
      this.setData({
        city:this.searchCity(e.detail.value)
      })
    }, 800)
  },

  onLoadAddress(option){
    this.setData({
      realAddress: app.globalData.realAddress
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getList()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})