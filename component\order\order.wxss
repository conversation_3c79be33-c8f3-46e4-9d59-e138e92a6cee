/* component/order/order.wxss */
@import '../../common.wxss';
.order{
  background-color: #fff;
  box-sizing: border-box;
}
.order_box_top{
  display: flex;
  align-items: center;
}
.order_box_top_let{
display: flex;
align-items: center;
}
.order_content{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.order_content_left{
  display: flex;
  flex-direction: column;
  height: 180rpx;
  justify-content: space-around;
  overflow: hidden;
}
.order_content_img{
  width: 180rpx;
  height: 180rpx;
}
.order_content_left_top{
  display: flex;
  align-items: center;
}
.timetext{
  background-color: #F5F7FB;
  height: 44rpx;
  width: 120rpx;
  color: #6B738B;
  border-radius: 6rpx;
}
.avatar{
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
}

.month_clock{
  display: flex;
  align-items: center;

  font-size: 40rpx;
  font-weight: bold;
}
.clock{
  color: var(--maingreencolor);
}
.order_bottom{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.money{
  color: var(--mainmoneycolor);
  flex: 1;
}
.order_bottom_right{
  font-size: 24rpx;
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.refund{
  border:  1rpx solid rgba(164, 169, 183, 0.20);
  width: 128rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  color: #A4A9B7;
}
.plus{
  background: var(--mainmoneycolor);
  color: #fff;
  width: 128rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;

}
.difference{
  background: var(--maingreencolor);
  color: #fff;
  width: 128rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
}

.more{
  width: 22rpx;
  height: 22rpx;
}

.t-red{
  color: #FF2121;
}