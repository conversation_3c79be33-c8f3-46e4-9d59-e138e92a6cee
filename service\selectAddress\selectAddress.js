// service/selectAddress/selectAddress.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    city: '',
    name: '',
    times: ''
  },
  selectAddress(e){
    let info = e.currentTarget.dataset.info
    let data = {
      province: info.pname,
      city: info.cityname,
      district: info.adname,
      name: info.name,
      lat: info.location.split(',')[1],
      lng: info.location.split(',')[0]
    }
    app.globalData.address = data
    util.back()
  },
  selectCity(){
    util.skip('/service/selectCity/selectCity',{
      'selectCity': (data)=>{
        this.setData({
          city: data,
          name: data
        })
        this.getList()
      }
    })
  },
  setName(e){
    this.setData({
      name: e.detail.value
    })
    clearTimeout(this.data.times)
    this.data.times = setTimeout(()=>{
      this.getList()
    }, 800)
  },
  getList(){
    http.get('user/getnearaddress',{
      city: this.data.city,
      name: this.data.name
    }, false, false).then(res => {
      this.setData({
        list: res.data
      })
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      city: options.city,
      name: options.name,
    })
    this.getList()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})