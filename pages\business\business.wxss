/* pages/business/business.wxss */
.busuness{
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing:  border-box;
}
.top-box{
  flex-shrink: 0;
}
.main-box{
  flex: 1;
  overflow: hidden;
}
.location_img{
  width: 24rpx;
  height: 24rpx;
}
.more{
  width: 24rpx;
  height: 24rpx;
}
.servers_top{
  display: flex;
  align-items: center;
}
.search_box{
  display: flex;
  align-items: center;
  background: #fff;
  width: 686rpx;
  padding: 20rpx;
  margin: 0 auto;
  box-sizing: border-box;
}
.search_img{
  width: 24rpx;
  height: 24rpx;
}
.input_box{
  width: 90%;
  color: #999;
}

.search{
  width: 95vw;
  margin: 0 auto;
  box-sizing: border-box;
}
.van-cell__left-icon-wrap{
  margin-top: 8rpx;
}

.right-icon{
  color: #fff;
  background-color: #1782FA;
  border-radius: 40rpx;
  width: 90rpx;
  height: 58rpx;
}

.scrollbox{
  width: 100vw;
  white-space: nowrap;
  height: 70rpx;
}
.tipsbox{
  display: flex;
  align-items: center;
}
.tipsitem{
  padding: 10rpx 20rpx;
  border-radius: 15rpx;
  font-size: 26rpx;
}
.active{
  font-size: 26rpx;
  padding: 10rpx 20rpx;
  border-radius: 15rpx;
  color: var(--maingreencolor);
}
.active:nth-child(n+2){
  margin-left: 40rpx;
}
.tipsitem:nth-child(n+2){
  margin-left: 40rpx;
}
.shopbox{
  height:100%;
  box-sizing: border-box;
}


.busuness .van-tabs__scroll {
  background: transparent ;
}