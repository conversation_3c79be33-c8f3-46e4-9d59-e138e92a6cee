<!--component/pack.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view bind:tap="tosetMealDetail" class="item-each z-padding-16 z-margin-t-24" bindtap="detail" data-info="{{info}}">
  <!-- 省 标签 -->
  <text class="sheng-tag">立省 ￥{{info.diffPrice}}</text>
  <image class="item-each-img-box z-margin-r-24" src="{{tool.cdn(info.image)}}" mode="aspectFill" lazy-load
    lazy-load-margin="0" />
  <view>
    <view class="setMeal-item-name">{{info.name}}</view>
    <view class="setMeal-item-gg">{{info.content}}</view>
    <view class="price_box_left z-padding-t-24">
      <view class="z-font-22 z-margin-t-8">￥</view>
      <view class="z-font-w">{{info.price}}</view>
    </view>
  </view>
</view>