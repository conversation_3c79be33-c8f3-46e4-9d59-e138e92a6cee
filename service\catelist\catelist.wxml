<!--service/tentype/tentype.wxml-->
<view class="catelist">
    <van-nav-bar
    title="{{title}}"
    left-arrow
    bind:click-left="onClickLeft"
    custom-style="background-color: #c8e2ff;"
    title-style="width: 136rpx; height: 36rpx; font-family: PingFangSC, PingFang SC; font-weight: 600; font-size: 34rpx; color: #333333; line-height: 36rpx; text-align: right; font-style: normal;"
  />

  <scroll-view scroll-x class="scrollbox z-margin-t-32" wx:if="{{cate.length > 1}}">
    <view class="tipsbox z-margin-l-32">
      <view class="z-margin-r-16 {{two_category_id==item.id?'active':'tipsitem'}}" bind:tap="choosetype" data-id="{{item.id}}"
        wx:for="{{cate}}" wx:key="*this">
        <image src="../../static/index/tuijian.png" wx:if="{{item.id==''&&two_category_id!=item.id}}"
          class="tuijian z-margin-r-8" mode="" />
        <image src="../../static/index/tuijians.png" wx:if="{{item.id==''&&two_category_id==item.id}}"
          class="tuijian z-margin-r-8" mode="" />
        {{item.label}}
      </view>
    </view>
  </scroll-view>
  <view class="main-box">
    <scroll-view scroll-y="true" class="scroll z-padding-24"  show-scrollbar="{{false}}" enhanced="true" bindscrolltolower="more">
      <block wx:for="{{list}}">
        <service info="{{item}}" type="{{2}}" bind:serviceTap="serviceTap"></service>
      </block>
      <van-empty description="暂无项目" wx:if="{{finish && list.length === 0}}" />
    </scroll-view>
  </view>
</view>