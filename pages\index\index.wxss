/* ==================== 全局样式 ==================== */
page {
  background: #FFFFFF;
  min-height: 100%;
  width: 100%;
  overflow-x: hidden;
}

/* ==================== 顶部区域 ==================== */
/* 顶部主容器 - 包含Logo和搜索栏 */
.header-section {
  padding: 20rpx;
  height: 325rpx;
  background-repeat: round;
  background-image: url(https://service.infooi.cn/uploads/20250727/78f34ced1ed8a66cc0a4a29023dbdd08.png);
  background-size: 100% 325rpx;
  display: flex;
  flex-direction: column;
}

/* Logo区域容器 */
.logo-area {
  margin-top: 95rpx;
}

/* Logo图片 */
.logo-image {
  width: 176rpx;
  height: 44rpx;
  
}

/* 搜索栏容器 */
.search-bar {
  box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.020000);
  background-color: rgba(255,255,255,1.000000);
  border-radius: 36rpx;
  display: flex;
  flex-direction: row;
  margin-top: 27rpx;
  padding: 18rpx 24rpx;
}

/* 搜索栏内容容器 */
.search-content {
  width: 332rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}

/* 搜索图标 */
.search-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 搜索占位符文字 */
.search-placeholder {
  overflow-wrap: break-word;
  color: rgba(153,153,153,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 6rpx;
}

/* ==================== 服务选择区域 ==================== */
/* 服务选择项 - 安装 */
.service-option-1 {
  width: 212rpx;
  height: 145.96rpx;
  background: url(https://service.infooi.cn/uploads/20250727/f9607095d5a9ecc631323591a26e0441.png) 0rpx -1rpx no-repeat;
  background-size: 212rpx 145.96rpx;
  display: flex;
  flex-direction: column;
}

/* 服务选择项 - 维修 */
.service-option-2 {
  width: 212rpx;
  height: 145.96rpx;
  background: url(https://service.infooi.cn/uploads/20250727/9a77d186acb4757c4f148a846020c980.png) 0rpx -1rpx no-repeat;
  background-size: 212rpx 145.96rpx;
  display: flex;
  flex-direction: column;
}

/* 服务选择项 - 保养换芯 */
.service-option-3 {
  width: 212rpx;
  height: 145.96rpx;
  background: url(https://service.infooi.cn/uploads/20250727/1258731fed43aa33e98ba60b2e9bf914.png) 0rpx -1rpx no-repeat;
  background-size: 212rpx 145.96rpx;
  display: flex;
  flex-direction: column;
}

/* ==================== 服务分类区域 ==================== */
/* 服务分类列表容器 */
.service-categories {
  min-height: 136rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 20rpx;
}

/* 服务分类项 */
.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
  flex: 1;
  min-width: 120rpx;
  max-width: 140rpx;
}

.service-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
}

.service-text {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}

/* ==================== 轮播图区域 ==================== */
/* 轮播图容器 */
.swiper-container {
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

/* 轮播图组件 */
.swiper {
  width: 100%;
  height: 262rpx;
}

/* 轮播图图片 */
.swiper-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}
/* ==================== 全局样式 ==================== */
page {
  background: #FFFFFF;
  min-height: 100%;
  width: 100%;
  overflow-x: hidden;
}

/* ==================== 顶部区域 ==================== */
/* 顶部主容器 - 包含Logo和搜索栏 */
.header-section {
  padding: 20rpx;
  height: 325rpx;
  background-repeat: round;
  background-image: url(https://service.infooi.cn/uploads/20250727/78f34ced1ed8a66cc0a4a29023dbdd08.png);
  background-size: 100% 325rpx;
  display: flex;
  flex-direction: column;
}

/* Logo区域容器 */
.logo-area {
  margin-top: 95rpx;
}

/* Logo图片 */
.logo-image {
  width: 176rpx;
  height: 44rpx;
  
}

/* 搜索栏容器 */
.search-bar {
  box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.020000);
  background-color: rgba(255,255,255,1.000000);
  border-radius: 36rpx;
  display: flex;
  flex-direction: row;
  margin-top: 27rpx;
  padding: 18rpx 24rpx;
}

/* 搜索栏内容容器 */
.search-content {
  width: 332rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}

/* 搜索图标 */
.search-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 搜索占位符文字 */
.search-placeholder {
  overflow-wrap: break-word;
  color: rgba(153,153,153,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 6rpx;
}

/* ==================== 服务选择区域 ==================== */
/* 服务选择项 - 安装 */
.service-option-1 {
  width: 212rpx;
  height: 145.96rpx;
  background: url(https://service.infooi.cn/uploads/20250727/f9607095d5a9ecc631323591a26e0441.png) 0rpx -1rpx no-repeat;
  background-size: 212rpx 145.96rpx;
  display: flex;
  flex-direction: column;
}

/* 服务选择项 - 维修 */
.service-option-2 {
  width: 212rpx;
  height: 145.96rpx;
  background: url(https://service.infooi.cn/uploads/20250727/9a77d186acb4757c4f148a846020c980.png) 0rpx -1rpx no-repeat;
  background-size: 212rpx 145.96rpx;
  display: flex;
  flex-direction: column;
}

/* 服务选择项 - 保养换芯 */
.service-option-3 {
  width: 212rpx;
  height: 145.96rpx;
  background: url(https://service.infooi.cn/uploads/20250727/1258731fed43aa33e98ba60b2e9bf914.png) 0rpx -1rpx no-repeat;
  background-size: 212rpx 145.96rpx;
  display: flex;
  flex-direction: column;
}

/* ==================== 服务分类区域 ==================== */
/* 服务分类列表容器 */
.service-categories {
  height: 136rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
}

/* 服务分类项 - 洗衣机维修 */
.service-item-0 {
  margin-right: 36rpx;
  display: flex;
  flex-direction: column;
}

.service-icon-0 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}

.service-text-0 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}

/* 服务分类项 - 冰箱维修 */
.service-item-1 {
  margin-right: 36rpx;
  display: flex;
  flex-direction: column;
}

.service-icon-1 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}

.service-text-1 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}

/* 服务分类项 - 油烟机维修 */
.service-item-2 {
  margin-right: 36rpx;
  display: flex;
  flex-direction: column;
}

.service-icon-2 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}

.service-text-2 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}

/* 服务分类项 - 空调维修 */
.service-item-3 {
  margin-right: 36rpx;
  display: flex;
  flex-direction: column;
}

.service-icon-3 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}

.service-text-3 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}

/* 服务分类项 - 全部服务 */
.service-item-4 {
  display: flex;
  flex-direction: column;
}

.service-icon-4 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}

.service-text-4 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}

/* ==================== 全局样式 ==================== */
page {
  background: #FFFFFF;
  min-height: 100%;
  width: 100%;
  overflow-x: hidden;
}

/* ==================== 顶部区域 ==================== */
/* 顶部主容器 - 包含Logo和搜索栏 */
.header-section {
  padding: 20rpx;
  height: 325rpx;
  background-repeat: round;
  background-image: url(https://service.infooi.cn/uploads/20250727/78f34ced1ed8a66cc0a4a29023dbdd08.png);
  background-size: 100% 325rpx;
  display: flex;
  flex-direction: column;
}

/* Logo区域容器 */
.logo-area {
  margin-top: 95rpx;
}

/* Logo图片 */
.logo-image {
  width: 176rpx;
  height: 44rpx;
  
}

/* 搜索栏容器 */
.search-bar {
  box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.020000);
  background-color: rgba(255,255,255,1.000000);
  border-radius: 36rpx;
  display: flex;
  flex-direction: row;
  margin-top: 27rpx;
  padding: 18rpx 24rpx;
}

/* 搜索栏内容容器 */
.search-content {
  width: 332rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}

/* 搜索图标 */
.search-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 搜索占位符文字 */
.search-placeholder {
  overflow-wrap: break-word;
  color: rgba(153,153,153,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 6rpx;
}

/* ==================== 服务选择区域 ==================== */
/* 服务选择项 - 安装 */
.service-option-1 {
  width: 212rpx;
  height: 145.96rpx;
  background: url(https://service.infooi.cn/uploads/20250727/f9607095d5a9ecc631323591a26e0441.png) 0rpx -1rpx no-repeat;
  background-size: 212rpx 145.96rpx;
  display: flex;
  flex-direction: column;
}

/* 服务选择项 - 维修 */
.service-option-2 {
  width: 212rpx;
  height: 145.96rpx;
  background: url(https://service.infooi.cn/uploads/20250727/9a77d186acb4757c4f148a846020c980.png) 0rpx -1rpx no-repeat;
  background-size: 212rpx 145.96rpx;
  display: flex;
  flex-direction: column;
}

/* 服务选择项 - 保养换芯 */
.service-option-3 {
  width: 212rpx;
  height: 145.96rpx;
  background: url(https://service.infooi.cn/uploads/20250727/1258731fed43aa33e98ba60b2e9bf914.png) 0rpx -1rpx no-repeat;
  background-size: 212rpx 145.96rpx;
  display: flex;
  flex-direction: column;
}

/* ==================== 服务分类区域 ==================== */
/* 服务分类列表容器 */
.service-categories {
  height: 136rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
}

/* 服务分类项 - 洗衣机维修 */
.service-item-0 {
  margin-right: 36rpx;
  display: flex;
  flex-direction: column;
}

.service-icon-0 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}

.service-text-0 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}

/* 服务分类项 - 冰箱维修 */
.service-item-1 {
  margin-right: 36rpx;
  display: flex;
  flex-direction: column;
}

.service-icon-1 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}

.service-text-1 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}

/* 服务分类项 - 油烟机维修 */
.service-item-2 {
  margin-right: 36rpx;
  display: flex;
  flex-direction: column;
}

.service-icon-2 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}

.service-text-2 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}

/* 服务分类项 - 空调维修 */
.service-item-3 {
  margin-right: 36rpx;
  display: flex;
  flex-direction: column;
}

.service-icon-3 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}

.service-text-3 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}

/* 服务分类项 - 全部服务 */
.service-item-4 {
  display: flex;
  flex-direction: column;
}

.service-icon-4 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}

.service-text-4 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}

/* ==================== 推广横幅区域 ==================== */
/* 推广横幅 */
.promotion-banner {
  margin: 20rpx;
  border-radius: 16rpx;
  background-image: url(https://service.infooi.cn/uploads/20250727/11bad3c7f44bdf3fa175be4889dd0095.png);
  height: 262rpx;
  background-repeat: round;
  display: flex;
  flex-direction: column;
}

/* ==================== 特色功能区域 ==================== */
/* 特色功能列表容器 */
.feature-list {
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx;
}
.feature-item {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.feature-icon {
  width: 28rpx;
  height: 28rpx;
  align-self: center;
  margin-right: 5rpx;
}

.feature-text {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 1rpx;
}

/* ==================== 服务优势区域 ==================== */
/* 服务优势标题 */
.section-title-advantages {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 32rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-left: 25rpx;
}

/* 优势卡片容器 */
.advantage-cards {
  flex-direction: row;
  display: flex;
  justify-content: space-around;
  margin: 20rpx;
}

/* 闪电上门优势卡片 */
.advantage-card-lightning {
  width: 342rpx;
  height:212rpx;
  /* background-size: 342rpx 212rpx; */
  background-repeat: round;
  background-image: url(https://service.infooi.cn/uploads/20250727/5deae86c56762c35302f8326d7f0c119.png);
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
}

.advantage-content-lightning {
  display: flex;
  flex-direction: column;
  padding: 30rpx 26rpx;
  padding-bottom: 10px;
}

.advantage-title-lightning {
  overflow-wrap: break-word;
  color: rgba(6,84,73,1);
  font-size: 30rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
  margin-right: 12rpx;
}

.advantage-desc-lightning {
  width: 132rpx;
  height: 64rpx;
  overflow-wrap: break-word;
  color: rgba(6,84,73,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 32rpx;
  margin-top: 20rpx;
}

.advantage-price-container {
  width: 132rpx;
  height: 34rpx;
  background: #6DC7BF;
  border-radius: 17rpx;
  text-align: center;
  line-height: 34rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.advantage-price-text {
  color: rgba(255,255,255,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  white-space: nowrap;
}

.advantage-price-arrow {
  width: 26rpx;
  height: 26rpx;
  margin-left: 5rpx;
}

/* 严选品质优势卡片 */
.yxpz{
  background-image: url(https://service.infooi.cn/uploads/20250727/dcd220dc3187e357d7b707c72fd824af.png)!important;
}
.sfrz{
  background-color: #6FA4C6!important;
}
.advantage-content-quality {
  display: flex;
  flex-direction: column;
}

.advantage-title-quality {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 32rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}

.advantage-desc-quality {
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 36rpx;
  margin-top: 16rpx;
}

.advantage-action-container {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 20rpx;
  display: flex;
  flex-direction: row;
  align-self: flex-end;
  padding: 8rpx 16rpx 8rpx 16rpx;
}

.advantage-action-button {
  display: flex;
  flex-direction: row;
}

.advantage-action-text {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 24rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}

/* ==================== 服务推荐区域 ==================== */
/* 服务推荐容器 */
.service-recommendations {
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx;
}

/* 省心省力特殊服务卡片 */
.special-service-card {
  width: 222rpx;
  height: 176rpx;
  background-size: 222rpx 176rpx;
  background-repeat: no-repeat;
  background-image: url(https://service.infooi.cn/uploads/20250727/464ec776144610c8896704ac6f91e778.png);
  padding-left: 18rpx;
  padding-top: 28rpx;
}

.special-service-title {
  overflow-wrap: break-word;
  color: #543A07;
  font-size: 30rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  white-space: nowrap;
}

.special-service-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 16rpx;
}

.special-service-details {
  display: flex;
  flex-direction: column;
}

.special-service-subtitle {
  overflow-wrap: break-word;
  color: #543A07;
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}

.special-service-price-container {
  width: 109rpx;
  height: 34rpx;
  background: #EDBD79;
  border-radius: 17rpx;
  border-radius: 20rpx;
  margin-top: 20rpx;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}
.special-service-price-text {
  overflow-wrap: break-word;
  font-size: 22rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
}

/* 家电维修服务卡片 */
.repair-service-card {
  width: 222rpx;
  height: 176rpx;
  background-size: 222rpx 176rpx;
  background-repeat: no-repeat;
  background-image: url(https://service.infooi.cn/uploads/20250727/72b40ed5953be1a60f844571e147d36e.png);
  padding-left: 18rpx;
  padding-top: 28rpx;

}

.repair-service-content {
  display: flex;
  flex-direction: column;
}

.repair-service-title {
  overflow-wrap: break-word;
  color: #55091C;
  font-size: 32rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}

.repair-service-desc {
  overflow-wrap: break-word;
  color: #55091C;
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 16rpx;
}

.repair-service-action {
  width: 109rpx;
  height: 34rpx;
  background: #F38FAA;
  border-radius: 17rpx;
  border-radius: 20rpx;
  margin-top: 20rpx;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.repair-service-button {
  font-size: 22rpx;
}

/* 家电清洗服务卡片 */
.cleaning-service-card {
  width: 222rpx;
  height: 176rpx;
  background-size: 222rpx 176rpx;
  background-repeat: no-repeat;
  background-image: url(https://service.infooi.cn/uploads/20250727/030d3ad6f1b6e3b0299b9e9c797bf096.png);
  padding-left: 18rpx;
  padding-top: 28rpx;
}

.cleaning-service-content {
  display: flex;
  flex-direction: column;
}

.cleaning-service-title {
  overflow-wrap: break-word;
  color: #07543C;
  font-size: 32rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}

.cleaning-service-desc {
  overflow-wrap: break-word;
  color: #07543C;
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 16rpx;
}

.cleaning-service-price-container {
  width: 109rpx;
  height: 34rpx;
  background: #6CC4A8;
  border-radius: 17rpx;
  border-radius: 20rpx;
  margin-top: 20rpx;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}



.cleaning-service-price-text {
  font-size: 24rpx;
}
.cleaning-service-arrow {
  width: 26rpx;
  height: 26rpx;
}

/* ==================== 服务流程区域 ==================== */
/* 服务流程标题 */
.section-title-process {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 32rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin: 20rpx;
}

/* 服务流程容器 */
.service-process {
  background-color: rgba(249,249,249,1.000000);
  border-radius: 16rpx;
  margin: 20rpx 20rpx 150rpx 20rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  padding: 40rpx 20rpx 40rpx 20rpx;
}
.process-step image{
  width: 28rpx;
  height: 28rpx;
}

/* 流程步骤 - 提交预约 */
.process-step-submit {
  display: flex;
  flex-direction: column;
}

.process-icon-submit {
  width: 80rpx;
  height: 80rpx;
  align-self: center;
}

.process-text-submit {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-top: 16rpx;
}

/* 流程步骤 - 师傅接单 */
.process-step-accept {
  display: flex;
  flex-direction: column;
}

.process-icon-accept {
  width: 80rpx;
  height: 80rpx;
  align-self: center;
}

.process-text-accept {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-top: 16rpx;
}

/* 流程步骤 - 上门服务 */
.process-step-service {
  display: flex;
  flex-direction: column;
}

.process-icon-service {
  width: 80rpx;
  height: 80rpx;
  align-self: center;
}

.process-text-service {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-top: 16rpx;
}

/* 流程步骤 - 修好付款 */
.process-step-payment {
  display: flex;
  flex-direction: column;
}

.process-icon-payment {
  width: 80rpx;
  height: 80rpx;
  align-self: center;
}

.process-text-payment {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-top: 16rpx;
}

/* 流程箭头 */
.process-arrow-1, .process-arrow-2, .process-arrow-3 {
  width: 28rpx;
  height: 28rpx;
  margin: 49rpx 0 49rpx 0;
}
