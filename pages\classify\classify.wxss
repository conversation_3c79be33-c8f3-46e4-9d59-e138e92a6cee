/* 顶部主容器 - 包含Logo和搜索栏 */
.header-section {
  padding: 20rpx;
  height: 582rpx;
  background: linear-gradient( 0, #FFFFFF 0%, #C5E0FF 99%);
  display: flex;
  flex-direction: column;
}

/* Logo区域容器 */
.logo-area {
  margin-top: 95rpx;
}

/* Logo图片 */
.logo-image {
  width: 176rpx;
  height: 44rpx;
  
}

/* 搜索栏容器 */
.search-bar {
  box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.020000);
  background-color: rgba(255,255,255,1.000000);
  border-radius: 36rpx;
  display: flex;
  flex-direction: row;
  margin-top: 27rpx;
  padding: 18rpx 24rpx;
}

/* 搜索栏内容容器 */
.search-content {
  width: 332rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}

/* 搜索图标 */
.search-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 搜索占位符文字 */
.search-placeholder {
  overflow-wrap: break-word;
  color: rgba(153,153,153,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 6rpx;
}

/* pages/classify/classify.wxss */
.classify {
  background-color: #EDF1F4;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.classify_box {
  display: flex;
  flex: 1;
  overflow: hidden;
  margin-top: -170px;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
}

.classify_left {
  overflow: hidden;
  color: #333333;
  height: 100%;
  width: 168rpx;
  /* background: #f7f7f7; */
  background: white;
  box-sizing: border-box;
}

.label{
  height: 124rpx;
  line-height: 124rpx;
  position: relative;
  background: #f7f7f7;
  font-size: 24rpx !important;
  color: #333333;
  text-align: center;
}
.active{
  background: #fff;
}

.below-active {
  border-radius: 0rpx 32rpx 0rpx 0rpx;
}


.active::before{
  content: '';
  width: 10rpx;
  height: 36rpx;
  background: #1782FA;
  position: absolute;
  left: 0;
  top: 44rpx;
}


/* ::v-deep .van-sidebar-item{
  width: 174rpx;
} */


.van-sidebar-item {
  padding-top: 30rpx;
  background-color: #fff !important;
}



.customclass {
  padding-top: 30rpx;
}

.van-sidebar-item--selected {
  border-color: #1782FA !important;
  background: #EDF1F4 !important;
  color: #000 !important;
}

.classify_right {
  box-sizing: border-box;
  width: 590rpx;
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;

}

.classify_right_con {
  flex: 1;
  overflow: hidden;
}

.classify_right_scroll {
  height: 100%;
  width: 100%;
}

.classify_right_box {

  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
}

.classify_right_item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.classify_right_box_img {
  width: 154rpx;
  height: 154rpx;
}