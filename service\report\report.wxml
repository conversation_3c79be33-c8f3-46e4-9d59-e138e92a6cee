<!--service/report/report.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<van-nav-bar
  title="举报投诉"
  left-arrow
  bind:click-left="onClickLeft"
  custom-style="background-color: #c8e2ff;"
  title-style="width: 136rpx; height: 36rpx; font-family: PingFangSC, PingFang SC; font-weight: 600; font-size: 34rpx; color: #333333; line-height: 36rpx; text-align: right; font-style: normal;"
/>
<view class="state z-padding-lr-32" wx:if="{{state===0}}">
      <image class="icons z-margin-r-16" src="../../static/service/underway.png"></image>
      平台处理中
</view>
<view class="state z-padding-lr-32" wx:if="{{state===1}}">
      <image class="icons z-margin-r-16" src="../../static/service/underway.png"></image>
      平台已处理
</view>

<view class="samebox  z-padding-32 z-font-28 z-margin-tb-32 z-radius-20" bind:tap="choose" data-name="{{item.name}}" wx:for="{{columns}}" wx:key="id">
  <view class="samebox_left">
    <view class="z-font-w z-font-26">{{item.name}}</view>
    <!-- <view class="text_999 z-font-22 z-margin-t-16"></view> -->
  </view>
  <image src="../../static/login/choosed.png"   wx:if="{{tool.includes(complaint_content,item.name)}}"  class="choose_img" mode=""/>
  <image src="../../static/login/choose.png" wx:else  class="choose_img" mode=""/>
</view>
<view class="z-margin-lr-32 z-margin-t-32 z-margin-b-60">
  <view class="z-font-26 z-font-w">补充说明</view>
  <view class="smcontent z-radius-20 z-padding-32 z-margin-tb-32">
    <textarea value="{{content}}" bindinput="setContent" placeholder="详细说明您遇到的问题，有助于平台更加地帮您解决" />
    <view class="z-flex-c image-box">
      <view class="z-margin-r-32 z-margin-b-24 each-img-min  images-box" wx:for="{{images}}"  data-index="{{index}}" bindtap="chooseImage">
          <image class="photo" src="{{tool.cdn(item)}}" mode="aspectFill"></image>
          <image src="/static/service/dels.png" class="del" catchtap="delImages" data-index="{{index}}"></image>
        </view>
        <view class="z-margin-r-32 z-margin-b-24 each-img-min" wx:if="{{images.length<9}}" bindtap="chooseImage">
          <image class="photo" src="/static/service/upload.png"></image>
        </view>
    </view>
  </view>
</view>

<view class="z-btn z-margin-32" bindtap="resave" wx:if="{{state===''}}">提交申请</view>
