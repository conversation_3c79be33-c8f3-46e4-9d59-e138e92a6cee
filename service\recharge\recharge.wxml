<view>
  <view class="head z-flex-c" style="padding-top:{{safeTop}};height: {{menuH}};box-sizing: content-box;">
    <view bind:tap="fanhui" class="fanhui">
      <image class="fanhui-img" src="/static/service/returnkey.png"></image>
    </view>
    <view class="head-title">充值</view>
  </view>
  <view class="head-box">
    <image class="bac-img" src="/static/service/recharger-bac.png"></image>

    <view class="yue">
      <view>
        <view class="z-margin-t-40 z-padding-l-32 yu-text">账户余额(元)</view>
        <view class="z-margin-t-10 z-padding-l-32 money">{{info.money}}</view>
        <view class="z-text-c detail-btn z-margin-l-32 z-margin-t-32" bind:tap="toDetail">
          余额明细
        </view>
      </view>
      <view>
        <image class="gold" src="/static/service/gold.png"></image>
      </view>
    </view>
  </view>

  <view class="z-padding-t-48 z-padding-lr-32 title">充值金额</view>
  <view class="z-flex-c money-box z-padding-t-40 z-padding-lr-32">
    <view class="each-money z-text-c {{price_id == item.id ? 'cur-each-money ' : ''}}" wx:for="{{packageList}}" wx:key="index"
      bind:tap="cut" data-current="{{item.id}}">￥<text class="z-font-40">{{item.price}}</text> <view>送{{item.gift_price}}</view> </view>
  </view>

  <!-- <view class="input z-padding-32 z-margin-t-32">
    <input placeholder="输入自定金额" type="digit" bindinput="setPrice" />
  </view> -->

  <view class="btn z-text-c" bindtap="pay">
    立即充值
  </view>

  <view class="agreement-box z-margin-t-32 z-flex-c" bind:tap="clickread">
    <image src="../../static/login/choose.png" wx:if="{{!agree}}" class="choose_img" mode="" />
    <image src="../../static/login/choosed.png" wx:else class="choose_img" mode="" />
    <view class="z-font-26 text_666 z-flex-c">已阅读并同意 <text class="t-color" catchtap="infoTap"
        data-info="recharge_info_id" data-name="用户充值协议">《用户充值协议》</text></view>
  </view>
</view>