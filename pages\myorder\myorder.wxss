/* service/myorder/myorder.wxss */
page{
  background-color: #f5f7f9;
}

.myorder{
height: 100vh;
display: flex;
flex-direction: column;
}
.header-section {
  padding: 20rpx;
  height: 582rpx;
  background: linear-gradient( 0, #FFFFFF 0%, #C5E0FF 99%);
  display: flex;
  flex-direction: column;
}
.header-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 34rpx;
  color: #333333;
  line-height: 36rpx;
  text-align: center;
  font-style: normal;
  margin-top: 90rpx;
}
.top-box{
  flex-shrink: 0;
  margin-top: -430rpx;
}
.van-tabs__scroll {
  background-color: transparent!important;
  overflow: auto;
}
.main-box{
  flex: 1;
  overflow: hidden;
}
.scroll-box{
  box-sizing: border-box;
  height: 100%;
}
.popbox{
  width: 686rpx;
  box-sizing: border-box;
}

.money{
  color: #FF9600;
}
.input_box{
  background-color: #F5F7F9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
input{
  text-align: right;
}
.popbox_bottom{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.popbox_bottom_money{
  display: flex;
  align-items: center;
}
.van-button{
  border-radius: 15rpx !important;
}
.plusbox{
  box-sizing: border-box;
}
.plus_item{
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #F5F7F9;
}
.plus_item_left{
  display: flex;
  flex-direction: column;
}

