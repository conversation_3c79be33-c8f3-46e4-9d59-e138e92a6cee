/* component/service.wxss */
@import '../../common.wxss';

.fourbox_item {
  width: 330rpx;
  background-color: #fff;
  box-sizing: border-box;
  flex-direction: column;
}

.fourbox_img {
  width: 100%;
  height: 282rpx;
}

.fourbox_tip {
  color: #1782FA;
  background: rgba(98, 203, 129, 0.1);
  display: inline-block;
  border: 1rpx solid #1782FA;
  height: 32rpx;
  line-height: 32rpx;
  padding: 0 4rpx;
}

.fourbox_name {
  display: flex;
  align-items: center;
}

.fourbox_name_left {
  color: #F7C566;
  background-color: #1C274C;
  width: 48rpx;
  height: 28rpx;
  border-radius: 4rpx;
  flex-shrink: 0;
}

.fourbox_tips {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;


}

.fourbox_tips_itme {
  display: flex;
  align-items: center;
}

.line {
  width: 1rpx;
  height: 18rpx;
  background-color: #EDEEF1;
  display: inline-block;
}

.shop_name_box {
  display: flex;
  align-items: center;
}

.shop_name {
  background-color: #FCF7EE;
  color: #DBAC54;
  height: 32rpx;

}

.shop_name_img {
  width: 25rpx;
  height: 25rpx;
}

.price_box {
  align-items: center;
  justify-content: space-between;
  display: flex;
  color: var(--mainmoneycolor);
}

.price_box_left {
  display: flex;
  align-items: center;
}


.goodsitem{
  background-color: #fff;
  box-sizing: border-box;
  display: flex;
}
.goodsitem_img{
  width: 180rpx;
  height: 180rpx;

}
.goodsitem_right{
  flex: 1;
  overflow: hidden;
}
.goodsitem_right_one{
display: flex;
align-items: center;
justify-content: space-between;
}
.goodsitem_right_one_left{
display: flex;
align-items: center;
overflow: hidden;
}
.tip{
  color: #F7C566;
  background-color: #1C274C;
  width: 48rpx;
  height: 28rpx;
  border-radius: 4rpx;
  flex-shrink: 0;
}


.goodsitem_right_three{
  display: flex;
  align-items: center;
}
.greentip{
  background: rgba(98,203,129,0.1);
  color: #1782FA;
  border: 1rpx solid #1782FA;
  height: 32rpx;
  line-height: 32rpx;
}
.yellowtip{
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FCF7EE;
  color: #DBAC54;
  height: 32rpx;
  border: 1rpx solid #FCF7EE;
}
.shopname{
  width: 24rpx;
  height: 24rpx;
}
.goodsitem_right_four{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.money{
  display: flex;
  color:var(--mainmoneycolor);
}
.buy{
  width: 104rpx;
  height: 48rpx;
  background:#1782FA;
  color: #fff;
}