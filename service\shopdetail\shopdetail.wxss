/* service/shopdetail/shopdetail.wxss */
.shop{
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.top-box{
  flex-shrink: 0;
  background: #fff;
  border-bottom: 1px solid rgba(164, 169, 183, 0.20);
}

.shopdetail_top{
  background-color: #fff;
}
.shopdetail_top_one{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.shop_img{
  width: 80rpx;
  height: 80rpx;
}
.shopdetail_top_one_left{
  display: flex;
  align-items: center;
}
.shopinfo{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 84rpx;
}
.shopinfo_top{
  display: flex;
  align-items: center;
}
.shopid{
  width: 32rpx;
  height: 32rpx;
}
.ratebox{
  background: rgba(255,150,0,0.1);
  color: var(--mainmoneycolor);
  display: flex;
  align-items: center;
}
.starimg{
  width: 18rpx;
  height: 18rpx;
}
.van-button{
  border-radius: 15rpx !important;
}
.shop_time_box{
  display: flex;
  align-items: center;
  background-color: #fff;
  box-sizing: border-box;
}
.shop_time{
  display: flex;
  align-items: center;
  background-color: #F5F7F9;
}
.follow{
  background: #1782FA;
  width: 112rpx;
  height: 56rpx;
  color: #fff;
}

.followed{
  border: 1px solid #EDEEF1;
  box-sizing: border-box;
  width: 112rpx;
  height: 56rpx;
  color: #A4A9B7;
}
.timetext{
  color: var(--maingreencolor);
}
.address_box{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid rgba(164, 169, 183, 0.20);
}
.address_box-left{
  display: flex;
  flex-direction: column;
  flex: 2;
}
.line{
  width: 1rpx;
  height: 57rpx;
  background-color: #EDEEF1;
}
.address_box_right{
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}
.address_box_right_item{
  display: flex;
  flex-direction: column;
}
.address_box_right_img{
  width: 40rpx;
  height: 40rpx;
}
.main-box{
  flex: 1;
}
.scroll{
  height: 100%;
  background-color: #EDF1F4;
  box-sizing: border-box;
}
