<!--pagesA/searchdetail/searchdetail.wxml-->
<view class="search_box">
  <view class="search">
    <van-search value="{{ value }}" shape="round" background="#EDF1F4" placeholder="请输入搜索关键词" use-right-icon-slot bind:change="onChange" bind:search="onSearch">
      <view class="right-icon z-font-22 z-flex" slot="right-icon" bind:tap="onSearch">搜索</view>
    </van-search>
  </view>
  <view class="history z-padding-lr-40" wx:if="{{history.length>0}}">
    <view class="text_999 z-font-24">历史记录</view>
    <image src="../../static/index/rubbish.png" bindtap="del" class="rubbish" mode="aspectFill" />
  </view>
  <view class="history_box ">
    <view class="history_item z-font-24 z-margin-b-16 z-radius-16 z-padding-tb-16 z-padding-lr-32" wx:for="{{history}}" wx:key="*this" data-info="{{item}}" bindtap="wordTap">
      {{item}}
    </view>
  </view>
  <view class="rankbox z-padding-32 z-radius-32" wx:if="{{list.length>0}}">
    <view class="rankbox_top">
      <view class="z-font-w">销量排行</view>
      <image src="/static/service/rank.png" class="rankimg" mode=""/>
    </view>
    <view class="rankbox_white z-padding-16 z-radius-16 z-font-24">
      <view class="rankbox_white_item z-margin-tb-16" wx:for="{{list}}" wx:key="index" data-info="{{item}}" bindtap="serviceTap">
        <view class="{{index==0?'one':index==1?'two':index==2?'three':'same'}} z-padding-lr-8 z-font-24 z-radius-8 z-margin-r-8">{{index+1}}</view>
        <view class="hidden">{{item.name}}</view>
      </view>
    </view>
  </view>
</view>