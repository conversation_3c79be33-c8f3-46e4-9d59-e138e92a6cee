<!-- component/order/order.wxml -->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="order z-padding-32 z-radius-20 z-margin-b-24" bindtap="orderTap">
  <view class="order_box_top">
    <view class="z-flex-c" wx:if="{{info.shopInfo}}" catchtap="orderShopTap">
      <view class="z-font-24 z-margin-r-8">{{info.shopInfo.abbr}}</view>
      <image src="/static/service/more.png" class="more"></image>
    </view>
    <view class="z-flex-1">
      <view class="z-font-w z-flex-1 hidden z-margin-l-16 z-font-28">
        {{info.orderDetail.name}}
      </view>
    </view>
    <view class="text_999 z-font-28" wx:if="{{info.status === 0}}">待支付</view>
    <view class="text_999 z-font-28 t-red" wx:elif="{{info.is_service === 1}}">售后中</view>
    <view class="text_999 z-font-28 t-red" wx:elif="{{info.is_service === 2}}">
      已退款 ¥{{info.refund_price}}
    </view>
    <view class="text_999 z-font-28" wx:elif="{{info.to_shop === 'shop' && info.status === 2}}">
      待核销
    </view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 1}}">订单已提交</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 2}}">订单已接单</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 3}}">已出发</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 4}}">已到达</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 5}}">服务中</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 6}}">已完成</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 7}}">已评价</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === -1}}">已取消</view>
  </view>
  <van-divider   customStyle="color: #F5F6FA; border-color: #F5F6FA; margin-top:25rpx"/>
  <view class="order_content z-margin-tb-24">
    <view class="order_content_left z-flex-1">
      <view class="order_content_left_top">
        <view class="timetext z-flex z-font-22">{{info.to_shop === 'door' ? '预约时间' : '到店核销'}}</view>
      </view>
      <view class="month_clock">
        <view class="month">
          {{info.to_shop === 'door' ? tool.formatTime(info.starttime, 'mm-dd') : ''}}
        </view>
        <view class="clock z-margin-l-24">
          {{info.to_shop === 'door' ? tool.formatTime(info.starttime, 'HH:MM') : ''}}
        </view>
      </view>
      <view class="text_999 z-font-24" wx:if="{{info.to_shop === 'shop' && info.shopName.trade_hour}}">
        {{info.shopName.trade_hour}}
      </view>
      <view class="text_999 z-font-24" wx:elif="{{info.status === 0}}">该订单还没有支付</view>
      <view class="text_999 z-font-24" wx:elif="{{info.status === 1}}">等待服务人员接单</view>
      <view class="z-flex-c" wx:elif="{{info.skillInfo}}">
        <image src="{{tool.cdn(info.skillInfo.image)}}" class="avatar z-margin-r-16" mode="aspectFill" lazy-load lazy-load-margin="0"></image>
        <view class="z-font-24">{{info.skillInfo.name}}</view>
      </view>
    </view>
    <image src="{{tool.cdn(info.orderDetail.image)}}" class="order_content_img z-radius-24 z-flex-0 z-margin-l-24" mode="aspectFill" lazy-load lazy-load-margin="0" />
  </view>
  <view class="order_bottom">
    <view class="money z-font-w">
      <view class="z-font-24 money-title">总付款</view>
      ￥{{info.payprice}}
    </view>
    <view class="order_bottom_right">
      <view class="plus z-radius-32 z-margin-l-24" data-type="4" catchtap="orderBtnTap" wx:if="{{info.status === 0}}">
        支付
      </view>
      <view class="refund z-radius-32 z-margin-l-24" data-type="1" catchtap="orderBtnTap" wx:if="{{info.status > 0 && info.is_service !== 2 && info.is_settle < 2}}">
        申请退款
      </view>
      <view class="plus z-radius-32 z-margin-l-24" data-type="2" catchtap="orderBtnTap" wx:if="{{info.status === 2 || info.status === 3 || info.status === 4 || info.status === 5}}">
        加项
      </view>
      <view class="difference z-radius-32 z-margin-l-24" data-type="3" catchtap="orderBtnTap" wx:if="{{info.status > 0 && info.status < 6}}">
        补差价
      </view>
      <view class="plus z-radius-32 z-margin-l-24" data-type="5" catchtap="orderBtnTap" wx:if="{{info.status === 6}}">
        去评价
      </view>
      <view class="plus z-radius-32 z-margin-l-24" data-type="6" catchtap="orderBtnTap" wx:if="{{info.status === 7}}">
        查看评价
      </view>
    </view>
  </view>
</view>

<!-- component/order/order.wxml
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="order z-padding-32 z-radius-20 z-margin-b-24" bindtap="orderTap">
  <view class="order_box_top">
    <view class="z-flex-c" wx:if="{{info.shopInfo}}" catchtap="orderShopTap">
      <view class="z-font-24 z-margin-r-8">{{info.shopInfo.abbr}}</view>
      <image src="/static/service/more.png" class="more"></image>
    </view>
    <view class="z-flex-1">
      <view class="z-font-w z-flex-1 hidden z-margin-l-16 z-font-28">
        {{info.orderDetail.name}}
      </view>
    </view>
    <view class="text_999 z-font-28" wx:if="{{info.status === 0}}">待支付</view>
    <view class="text_999 z-font-28 t-red" wx:elif="{{info.is_service === 1}}">售后中</view>
    <view class="text_999 z-font-28 t-red" wx:elif="{{info.is_service === 2}}">
      已退款 ¥{{info.refund_price}}
    </view>
    <view class="text_999 z-font-28" wx:elif="{{info.to_shop === 'shop' && info.status === 2}}">
      待核销
    </view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 1}}">订单已提交</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 2}}">订单已接单</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 3}}">已出发</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 4}}">已到达</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 5}}">服务中</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 6}}">已完成</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === 7}}">已评价</view>
    <view class="text_999 z-font-28" wx:elif="{{info.status === -1}}">已取消</view>
  </view>
  <view class="order_content z-margin-tb-32">
    <view class="order_content_left z-flex-1">
      <view class="order_content_left_top">
        <view class="timetext z-flex z-font-22">{{info.to_shop === 'door' ? '预约时间' : '到店核销'}}</view>
        <view class="z-font-w z-flex-1 hidden z-margin-l-16 z-font-28">
          {{info.orderDetail.name}}
        </view>
      </view>
      <view class="month_clock">
        <view class="month">
          {{info.to_shop === 'door' ? tool.formatTime(info.starttime, 'mm-dd') : ''}}
        </view>
        <view class="clock z-margin-l-24">
          {{info.to_shop === 'door' ? tool.formatTime(info.starttime, 'HH:MM') : ''}}
        </view>
      </view>
      <view class="text_999 z-font-24" wx:if="{{info.to_shop === 'shop' && info.shopName.trade_hour}}">
        {{info.shopName.trade_hour}}
      </view>
      <view class="text_999 z-font-24" wx:elif="{{info.status === 0}}">该订单还没有支付</view>
      <view class="text_999 z-font-24" wx:elif="{{info.status === 1}}">等待服务人员接单</view>
      <view class="z-flex-c" wx:elif="{{info.skillInfo}}">
        <image src="{{tool.cdn(info.skillInfo.image)}}" class="avatar z-margin-r-16" mode="aspectFill" lazy-load lazy-load-margin="0"></image>
        <view class="z-font-24">{{info.skillInfo.name}}</view>
      </view>
    </view>
    <image src="{{tool.cdn(info.orderDetail.image)}}" class="order_content_img z-radius-24 z-flex-0 z-margin-l-24" mode="aspectFill" lazy-load lazy-load-margin="0" />
  </view>
  <van-divider />
  <view class="order_bottom">
    <view class="money z-font-w">￥{{info.payprice}}</view>
    <view class="order_bottom_right">
      <view class="plus z-radius-32 z-margin-l-24" data-type="4" catchtap="orderBtnTap" wx:if="{{info.status === 0}}">
        支付
      </view>
      <view class="refund z-radius-32 z-margin-l-24" data-type="1" catchtap="orderBtnTap" wx:if="{{info.status > 0 && info.is_service !== 2 && info.is_settle < 2}}">
        申请退款
      </view>
      <view class="plus z-radius-32 z-margin-l-24" data-type="2" catchtap="orderBtnTap" wx:if="{{info.status === 2 || info.status === 3 || info.status === 4 || info.status === 5}}">
        加项
      </view>
      <view class="difference z-radius-32 z-margin-l-24" data-type="3" catchtap="orderBtnTap" wx:if="{{info.status > 0 && info.status < 6}}">
        补差价
      </view>
      <view class="plus z-radius-32 z-margin-l-24" data-type="5" catchtap="orderBtnTap" wx:if="{{info.status === 6}}">
        去评价
      </view>
      <view class="plus z-radius-32 z-margin-l-24" data-type="6" catchtap="orderBtnTap" wx:if="{{info.status === 7}}">
        查看评价
      </view>
    </view>
  </view>
</view> -->