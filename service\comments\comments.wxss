/* service/comments/comments.wxss */
.comment{
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.tipsbox{
  display: flex;
  overflow-x: auto;

}
.tipsbox::-webkit-scrollbar{
  display: none;
}

.tipsitem{
  height: 56rpx;
  background-color: #fff;
  color: #6B738B;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-sizing: border-box;
}
.active{
  background-color: #1782FA;
  color: #fff;
}
.mian-box{
  flex: 1;
  overflow: hidden;
}

.scroll-box{
  height: 100%;
  box-sizing: border-box;
}
