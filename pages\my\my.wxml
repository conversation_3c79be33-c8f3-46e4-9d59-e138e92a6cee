<!--pages/mytest/mytest.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="my_top">
  <image src="https://service.infooi.cn/uploads/20250727/66bcc8935534c3a96116711dcb747749.png" class="mybg" mode="aspectFill" />
  <view class="myinfo z-margin-lr-24" style="padding-top: {{safeTop}}">
    <image class="z-margin-r-32 avatar" wx:if="{{info}}" src="{{tool.cdn(info.avatar)}}" />
    <image class="z-margin-r-32 avatar" wx:else src="{{tool.cdn(config.user_image)}}" bindtap="login" />
    <view class="myinfo_text">
      <view wx:if="{{info}}" class="info-details">
        <view>
          <view class="z-font-40 info-item1">{{info.nickname}}</view>
          <view class="z-flex" style="margin-top: 10rpx;">
            <view class="z-font-24 info-item2">{{info.mobile}}</view>
            <view class="info-renzheng">未认证
              <van-icon name="arrow" />
            </view>
          </view>
        </view>
        <view bindtap="togrzl">
          <van-icon name="/static/my/setting.png" size="40rpx" />
        </view> 
      </view>
      <text class="z-font-40" wx:else bindtap="login">请先登录</text>
      <view class="namebox_text z-padding-r-16 z-margin-t-16 z-font-22 " wx:if="{{info.plusInfo.is_plus > 0}}">
        <image src="../../static/index/vip.png" class="vip" mode="" />
        <view class="plusname z-flex">{{info.plusInfo.plusname}}</view>
      </view>
    </view>
  </view>
  <view class="all_box z-radius-24 " style="display: none;">
    <view class="menber_box  z-padding-24" bindtap="skip" data-url="/service/level/level">
      <view class="menber_box_left">
        <image src="../../static/index/king.png" class="king_img z-margin-r-16" mode="" />
        <view class="menber_box_text">
          <view class="z-font-w menber_text">家政会员</view>
          <view class="z-font-24 text_999 z-margin-t-16">开通会员可享受项目会员价</view>
        </view>
      </view>
      <view class="poenit z-padding-tb-8 z-padding-lr-24 z-font-28 z-radius-24 z-font-w">
        {{info.plusInfo.is_plus > 0 ? '立即续费' : '立即开通'}}
      </view>
    </view>
    <view class="fourbox z-radius-24 z-padding-24">
      <view class="fourboxitem" bindtap="skip" data-url="/service/focus/focus?active=0&name=关注服务者">
        <view class="z-font-w">{{info.skillFollow || 0}}</view>
        <view class="z-font-24 text_666 z-margin-t-8">关注服务者</view>
      </view>
      <view class="fourboxitem" bindtap="skip" data-url="/service/focus/focus?active=1&name=收藏项目">
        <view class="z-font-w">{{info.goodsFollow || 0}}</view>
        <view class="z-font-24 text_666 z-margin-t-8">收藏项目</view>
      </view>
      <view class="fourboxitem" bindtap="skip" data-url="/service/focus/focus?active=2&name=关注店铺">
        <view class="z-font-w">{{info.shopFollow || 0}}</view>
        <view class="z-font-24 text_666 z-margin-t-8">关注店铺</view>
      </view>
    </view>
    <view class="z-flex-c z-padding-lr-32 z-padding-b-32">
      <view class="more z-padding-24 z-radius-20" bindtap="skip" data-url="/service/mycoup/mycoup">
        <image class="icon" src="/static/index/coupon-icon.png"></image>
        <view class="z-font-26 pos ">我的优惠券</view>
        <view class="pos z-margin-t-16">
          <text class="z-font-30 num z-padding-r-16">{{info.couponCount || 0}}</text>
          <text class="z-font-22 text_666">张未使用</text>
        </view>
      </view>
      <view class="z-flex-1"></view>
      <view class="more z-padding-24 z-radius-20" bindtap="skip" data-url="/service/recharge/recharge">
        <image class="icon" src="/static/index/wallet-icon.png"></image>
        <view class="z-font-26 pos">我的钱包</view>
        <view class="pos z-margin-t-16">
          <text class="z-font-30 num z-padding-r-16">{{info.money || 0}}</text>
          <text class="z-font-22 text_666">元</text>
        </view>
      </view>
    </view>
  </view>
  <view class="order_box z-radius-24 z-padding-32">
    <view class="order_box_top" bindtap="myOrder" data-type="1" data-active="1">
      <view class="z-font-30">我的订单</view>
      <view class="z-font-24 text_999">你有一个开单待付款
        <van-icon name="arrow" /> 
      </view>
    </view>
    <view class="fourbox z-margin-t-32">
      <view class="fourboxitem" bindtap="myOrder" data-type="1" data-active="2">
        <view class="num-box">
          <image src="../../static/my/djd.png" class="fourbox_img" mode="" />
          <view class="point z-flex" wx:if="{{info && info.orderCount.unServiceCount > 0}}">{{info.orderCount.unServiceCount}}
          </view>
        </view>
        <view class="z-margin-t-16 z-font-28">待接单</view>
      </view>
      <view class="fourboxitem" bindtap="myOrder" data-type="1" data-active="3">
        <view class="num-box">
          <image src="../../static/my/fwz.png" class="fourbox_img" mode="" />
          <view class="point z-flex" wx:if="{{info && info.orderCount.serviceCount > 0}}">{{info.orderCount.serviceCount}}
          </view>
        </view>

        <view class="z-margin-t-16 z-font-28">服务中</view>
      </view>
      <view class="fourboxitem" bindtap="myOrder" data-type="2" data-active="0">
        <view class="num-box">
          <image src="../../static/my/ywc.png" class="fourbox_img" mode="" />
          <view class="point z-flex" wx:if="{{info && info.orderCount.notCommentCount > 0}}">
            {{info.orderCount.notCommentCount}}</view>
        </view>

        <view class="z-margin-t-16 z-font-28">已完成</view>
      </view>
      <view class="fourboxitem"  bindtap="myOrder" data-type="1" data-active="0">
        <view class="num-box">
          <image src="../../static/my/qbdd.png" class="fourbox_img" mode="" />
          <view class="point z-flex" wx:if="{{info && info.orderCount.refundCount > 0}}">{{info.orderCount.refundCount}}</view>
        </view>
        <view class="z-margin-t-16 z-font-28">全部订单</view>
      </view>
    </view>
  </view>

  <view class="setting_box z-margin-t-24 z-radius-24 z-padding-lr-32 z-padding-t-40 z-padding-b-16" style="display: none;">
    <view class="setting_box_item" bindtap="skip" data-url="/service/question/question">
      <image src="../../static/index/help.png" class="setting_box_img" mode="" />
      <view class="z-font-24 z-margin-t-16">帮助中心</view>
    </view>
    <view class="setting_box_item" bindtap="skip" data-url="/service/feedback/feedback">
      <image src="../../static/index/feedback.png" class="setting_box_img" mode="" />
      <view class="z-font-24 z-margin-t-16">意见反馈</view>
    </view>
    <view class="setting_box_item">
      <image src="../../static/index/lxkf.png" class="setting_box_img" mode="" />
      <view class="z-font-24 z-margin-t-16">联系客服</view>
      <button type="primary" open-type="contact" class="contact"></button>
    </view>
    <view class="setting_box_item" bindtap="skip" data-url="/service/couponscenter/couponscenter">
      <image src="../../static/index/lqzx.png" class="setting_box_img" mode="" />
      <view class="z-font-24 z-margin-t-16">优惠券</view>
    </view>
    <view class="setting_box_item" bindtap="skip" data-url="/service/comments/comments?type=user&id={{info.id}}">
      <image src="../../static/index/wdpj.png" class="setting_box_img" mode="" />
      <view class="z-font-24 z-margin-t-16">我的评价</view>
    </view>
    <view class="setting_box_item" bindtap="toService">
      <image src="../../static/index/fwjd.png" class="setting_box_img" mode="" />
      <view class="z-font-24 z-margin-t-16">服务接单</view>
    </view>
    <view class="setting_box_item" bindtap="toShop">
      <image src="../../static/index/sjrz.png" class="setting_box_img" mode="" />
      <view class="z-font-24 z-margin-t-16">商家入驻</view>
    </view>
    <view class="setting_box_item" bindtap="skip" data-url="/service/addressmanager/addressmanager">
      <image src="../../static/index/fwdz.png" class="setting_box_img" mode="" />
      <view class="z-font-24 z-margin-t-16">服务地址</view>
    </view>
    <view class="setting_box_item" bindtap="skip" data-url="/service/notice/notice">
      <image src="../../static/index/notice.png" class="setting_box_img" mode="" />
      <view class="z-font-24 z-margin-t-16">平台公告</view>
    </view>
    <view class="setting_box_item" bindtap="skip" data-url="/service/setpage/setpage">
      <image src="../../static/index/set.png" class="setting_box_img" mode="" />
      <view class="z-font-24 z-margin-t-16">设置</view>
    </view>
  </view>


  <!--service/setpage/setpage.wxml-->
  <view class="setting_box z-margin-t-24 z-radius-24 ">
    <!-- <van-cell-group>
      <van-cell size='large' is-link title="个人资料" link-type="navigateTo" url="/service/myinfo/myinfo" />
      <van-cell size='large' is-link title="用户协议" link-type="navigateTo" url="/service/info/info?id={{agreement_id}}&name=用户协议" />
      <van-cell size='large' is-link title="隐私政策" link-type="navigateTo" url="/service/info/info?id={{privacy_id}}&name=隐私政策" />
      <van-cell size='large' is-link title="关于我们" link-type="navigateTo" url="/service/about/about" />
    </van-cell-group> -->
    <!-- 自定义cell样式，保留原有数据和功能 -->
    <view class="custom-cell-group">
      <view class="custom-cell-item" bindtap="skip" data-url="/service/addressmanager/addressmanager">
        <view class="custom-cell-left">
          <image src="../../static/my/wddz.png" class="custom-cell-icon" mode="" />
          <text class="custom-cell-title">我的地址</text>
        </view>
        <van-icon name="arrow" />
      </view>
      
      <view class="custom-cell-item" bindtap="skip" data-url="/service/focus/focus?active=1&name=收藏项目">
        <view class="custom-cell-left">
          <image src="../../static/my/wdsc.png" class="custom-cell-icon" mode="" />
          <text class="custom-cell-title">我的收藏</text>
        </view>
        <van-icon name="arrow" />
      </view>
      
      <view class="custom-cell-item" bindtap="skip" data-url="/service/feedback/feedback">
        <view class="custom-cell-left">
          <image src="../../static/my/tsjy.png" class="custom-cell-icon" mode="" />
          <text class="custom-cell-title">投诉建议</text>
        </view>
        <van-icon name="arrow" />
      </view>
      
      <view class="custom-cell-item">
        <view class="custom-cell-left">
          <image src="../../static/my/kfzx.png" class="custom-cell-icon" mode="" />
          <text class="custom-cell-title">客服中心</text>
        </view>
        <van-icon name="arrow" />
        <button type="primary" open-type="contact" class="contact"></button>
      </view>
      
      <view class="custom-cell-item" bindtap="skip" data-url="/service/info/info?id={{privacy_id}}&name=隐私政策及用户协议">
        <view class="custom-cell-left">
          <image src="../../static/my/ysxy.png" class="custom-cell-icon" mode="" />
          <text class="custom-cell-title">隐私政策及用户协议</text>
        </view>
        <van-icon name="arrow" />
      </view>
    </view>
  </view>

  <view class="logout z-margin-t-24 z-margin-b-100" bindtap="out">
    退出登录
  </view>

</view>