// component/order/order.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    info: Object
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    orderShopTap(){
      this.triggerEvent('orderShopTap',this.data.info)
    },
    orderBtnTap(e){
      this.triggerEvent('orderBtnTap',{info:this.data.info,type:e.currentTarget.dataset.type})
    },
    orderTap(e){
      this.triggerEvent('orderTap',this.data.info)
    }
  }
})
