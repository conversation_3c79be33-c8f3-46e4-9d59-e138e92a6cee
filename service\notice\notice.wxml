<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="notice">
  <scroll-view class="scroll-box z-padding-t-24 z-padding-lr-32" scroll-y="true" show-scrollbar="{{false}}"
    enhanced="true" bindscrolltolower="more">
    <view class="list z-radius-20  each-item z-flex-c-s-b z-padding-32 z-border-b" wx:for="{{list}}"
      wx:key="index" data-id="{{item.id}}" bindtap="detail">
      <view class="item-text z-margin-r-24">
        <view class="title">{{item.title}}</view>
        <view class="z-margin-t-8 z-font-22 learn-text theme-color">{{tool.formatTime(item.createtime)}}</view>
      </view>
      <image class="item-img z-radius-16" wx:if="{{item.image}}" mode="aspectFill" src="{{tool.cdn(item.image)}}"></image>
    </view>
    <van-empty description="暂无订单" wx:if="{{finish && list.length === 0}}" />
  </scroll-view>
</view>