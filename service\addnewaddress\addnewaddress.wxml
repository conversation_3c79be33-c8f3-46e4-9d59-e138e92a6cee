<!--service/addnewaddress/addnewaddress.wxml-->
<view class="address">
  <view class="main-box">
    <view class="addbox z-margin-32">
      <view>服务地址</view>
      <view class="input_box z-padding-32 z-radius-16 z-margin-tb-32" bindtap="chooseAddress">
        <input type="text" placeholder="输入服务地址" value="{{info}}" disabled="true" />
      </view>
    </view>
    <view class="addbox z-margin-32">
      <view>门牌号</view>
      <view class="input_box z-padding-32 z-radius-16 z-margin-tb-32">
        <input type="text" placeholder="请输入详细地址 例：1号楼1单元201室" value="{{address}}" bindinput="setAddress" />
      </view>
    </view>

    <view class="addbox z-margin-32">
      <view>联系人</view>
      <view class="sinput_box">
        <view class="input_box z-padding-32 z-radius-16 z-margin-tb-32">
          <input type="text" placeholder="请输入联系人姓名" value="{{name}}" bindinput="setName" />
        </view>
        <view class="sex_box">
          <view class="men" bind:tap="choosesex">
            <image src="/static/service/greencircle.png" wx:if="{{sex==1}}" class="greencircle" mode="" />
            <image src="/static/login/choose.png" wx:else class="greencircle" mode="" />
            <view class="z-margin-l-16">先生</view>
          </view>
          <view class="men" bind:tap="choosesex">
            <image src="/static/service/greencircle.png" wx:if="{{sex==0}}" class="greencircle" mode="" />
            <image src="/static/login/choose.png" wx:else class="greencircle" mode="" />
            <view class="z-margin-l-16">女士</view>
          </view>
        </view>
      </view>
    </view>
    <view class="addbox z-margin-32">
      <view>联系电话</view>
      <view class="input_box z-padding-32 z-radius-16 z-margin-tb-32">
        <input type="number" placeholder="请输入电话号码" value="{{mobile}}" bindinput="setMobile" />
      </view>
    </view>

    <view class="defaultaddress" bind:tap="choosedefault">
      <view class="defaultaddress_top">
        <view class="z-font-w">设为默认地址</view>
        <image src="../../static/login/choose.png" wx:if="{{!state}}" class="greencircle" mode="" />
        <image src="../../static/login/choosed.png" wx:else class="greencircle" mode="" />
      </view>
      <view class="text_999 z-font-24 z-margin-t-24">提示：下单时会优先使用该地址</view>
    </view>
  </view>
  <view class="sure z-padding-t-32"  style="padding-bottom: {{safeBottom}}">
    <view class="btnbox">
      <van-button type="primary" color="#1782fa" round size='large' bindtap="save">确定</van-button>
    </view>
  </view>
</view>