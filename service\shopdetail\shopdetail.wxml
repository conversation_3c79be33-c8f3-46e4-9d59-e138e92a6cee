<!--service/shopdetail/shopdetail.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="shop">
  <view class="top-box">
    <view class="shopdetail_top_one z-padding-32">
      <view class="shopdetail_top_one_left" bind:tap="toaptitude">
        <image src="{{tool.cdn(info.logo_image)}}" class="shop_img z-radius-16 z-margin-r-32" mode="aspectFill" />
        <view class="shopinfo">
          <view class="shopinfo_top">
            <view>{{info.abbr}}</view>
            <image src="../../static/index/shopid.png" class="shopid z-margin-lr-8 z-flex-0" mode="" />
            <van-icon name="arrow" color='#999' size='15' />
          </view>
          <view class="shopinfo_top">
            <view class="ratebox  z-padding-lr-24 z-radius-16 z-margin-r-24">
              <image src="../../static/index/star.png" class="starimg" mode="" />
              <view class="z-font-24 z-margin-l-8">{{tool.toFix(info.commentScore,1)}}</view>
            </view>
            <view class="text_999 z-font-24">{{info.followNum}}人关注</view>
          </view>
        </view>
      </view>
      <view class="followed z-flex z-radius-4 z-font-22" wx:if="{{info.followState}}" bindtap="collect">已关注</view>
    <view class="follow z-flex z-radius-4 z-font-22" wx:else bindtap="collect">关注</view>
    </view>
    <view class="shop_time_box z-margin-lr-32 z-margin-b-24 z-font-24" wx:if="{{info.trade_hour}}">
      <view class="shop_time z-radius-16 z-padding-lr-32 z-padding-tb-8">
        <view class="timetext">营业时间：</view>
        <view>{{info.trade_hour}}</view>
      </view>
    </view>
    <view class="address_box z-padding-tb-24 z-padding-lr-32" wx:if="{{info.to_shop}}">
      <view class="address_box-left">
        <view class="z-font-24">{{info.address}}</view>
        <view class="text_999 z-font-24 z-margin-t-8">{{info.province}}{{info.city}}{{info.district}}</view>
      </view>
      <view class="address_box_right z-margin-l-16">
        <view class="line"></view>
        <view class="address_box_right_item" bindtap="openLoaction">
          <image src="../../static/index/shopposition.png" class="address_box_right_img" mode="" />
          <view class="z-font-22 z-margin-t-16 text_999">导航</view>
        </view>
        <view class="address_box_right_item" bindtap="call">
          <image src="../../static/index/phone.png" class="address_box_right_img" mode="" />
          <view class="z-font-22 z-margin-t-16 text_999">电话</view>
        </view>
      </view>
    </view>
  </view>
  <view>
    <van-tabs active="{{ active }}" bind:change="onChange" color='#1782FA'>
      <van-tab title="全部项目"></van-tab>
      <van-tab title="优惠券"></van-tab>
      <van-tab title="用户评价"></van-tab>
    </van-tabs>
    <!-- 项目 -->
    <view class="main-box">
      <scroll-view scroll-y="true" class="scroll z-padding-32" how-scrollbar="{{false}}" enhanced="true"
        bindscrolltolower="more">
        <block wx:for="{{list}}" wx:if="{{active === 0}}"  wx:key="index">
          <service info="{{item}}" type="{{2}}" bind:serviceTap="serviceTap"></service>
        </block>
        <block wx:for="{{list}}" wx:key="*this" wx:if="{{active === 1}}"  wx:key="index">
          <coupon info="{{item}}" type="{{1}}" bind:couponTap="couponTap"></coupon>
        </block>
        <block wx:for="{{list}}" wx:if="{{active === 2}}"  wx:key="index">
          <comment info="{{item}}"></comment>
        </block>
        <van-empty description="暂无内容" wx:if="{{finish && list.length === 0}}" />
      </scroll-view>
    </view>


  </view>
</view>