/* service/gocomment/gocomment.wxss */
page {
  background-color: #fff;
}

.rate_box {
  width: 686rpx;
  background-color: #F5F7F9;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.choosetips {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.choosetips_item {
  background-color: #F5F7F9;
  padding: 15rpx 20rpx;
  color: #999;
  border-radius: 10rpx;
  margin: 16rpx;
  box-sizing: border-box;
}

.active {
  background: rgba(98, 203, 129, 0.1);
  padding: 15rpx 20rpx;
  color: #999;
  border-radius: 10rpx;
  margin: 16rpx;
  border:  1rpx solid var(--maingreencolor);
  box-sizing: border-box;
}
.smcontent{
  width: 686rpx;
  background-color: #eff4f8;
  box-sizing: border-box;
}

.content{
  width: 100%;
}

.box{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.images-box{
  position: relative;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}
.images-box:nth-of-type(3n){
  margin-right: 0;
}
.del{
  position: absolute;
  width: 26rpx;
  height: 26rpx;
  right: 0;
  top: -13rpx;
  z-index: 10;
}

.icon{
  width: 180rpx;
  height: 180rpx;
}