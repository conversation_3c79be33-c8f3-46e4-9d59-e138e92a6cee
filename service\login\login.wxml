<!--service/login/login.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<van-nav-bar
  title="登录"
  left-arrow
  bind:click-left="onClickLeft"
  custom-style="background-color: #c8e2ff;"
  title-style="width: 136rpx; height: 36rpx; font-family: PingFangSC, PingFang SC; font-weight: 600; font-size: 34rpx; color: #333333; line-height: 36rpx; text-align: right; font-style: normal;"
/>
<view class="z-flex">
  <image src="{{tool.cdn(config.user_image)}}" class="logo_img" mode=""/>
</view>
<view class="z-flex">
<view class="z-font-w title">
  {{config.user_name}}
</view>

</view>
<view class=" z-flex z-margin-t-32 text_999 z-font-28">{{config.user_intro}}</view>

<view class="z-flex btn-box" style="margin-top: 200rpx;" >
  <button open-type="getPhoneNumber" class="btn" bindgetphonenumber="getPhoneNumber">手机号快捷登录</button>
  <view class="mask z-margin-l-32" bind:tap="read" wx:if="{{!agree}}"></view>
</view>
<view class="z-flex" bind:tap="clickread">
  <image src="../../static/login/choose.png"   wx:if="{{!agree}}" class="choose_img" mode=""/>
  <image src="../../static/login/choosed.png" wx:else  class="choose_img" mode=""/>
  <view class="text_999 z-font-28 z-margin-l-8">已阅读并同意 <text class="money" catchtap="infoTap" data-info="user_agreement_info_id" data-name="用户协议">《用户协议》</text> 和 <text class="money" catchtap="infoTap" data-info="user_privacy_info_id" data-name="隐私政策">《隐私政策》</text> </view>
</view>
<van-popup show="{{ show }}" position="bottom" close-on-click-overlay="true"  bind:close="onClose">
  <view class="z-font-30 z-font-w z-margin-32">完善个人信息</view>
  <view class="info-box z-margin-lr-32 z-padding-lr-32 z-padding-tb-8 z-radius-20">
    <view class="avatar-box z-margin-tb-24 z-padding-tb-12 z-flex-c">
      <view class="z-font-22 text_666 z-margin-r-32">头像</view>
      <image class="avatar z-radius" src="{{tool.cdn(avatarUrl)}}"></image>
      <view class="z-flex-1"></view>
      <van-icon name="arrow" />
      <button class="z-absolute" open-type="chooseAvatar" bind:chooseavatar="chooseAvatar"></button>
    </view>
    <view class="avatar-box z-margin-tb-24 z-flex-c">
      <view class="z-font-22 text_666 z-margin-r-32">昵称</view>
      <input type="nickname" class="z-flex-1 z-font-28" value="{{nickname}}"  placeholder="请输入昵称" bindnicknamereview="namereview" bindinput="nameInput" />
    </view>
  </view>
  <view class="z-padding-32" style="padding-bottom:{{safeBottom}}">
      <view class="z-btn" bindtap="saveInfo">保存</view>
    </view>
</van-popup>

