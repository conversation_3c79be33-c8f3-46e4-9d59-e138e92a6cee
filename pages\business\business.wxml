<!--pages/business/business.wxml-->
<view class="busuness">
  <view class="top-box">
    <view class="servers_top z-margin-lr-32" style="padding-top:{{safeTop}};height: {{menuH}};box-sizing: content-box;"  bindtap="selectAddress">
      <image src="/static/index/blocation.png" class="location_img z-margin-r-16" mode="" />
      <view class="z-font-32">{{address.name}}</view>
      <image src="/static/index/black-more.png" class="more"></image>
    </view>
    <view class="search">
      <van-search value="{{ value }}" shape="round" background="transparent" placeholder="请输入商家名" use-right-icon-slot
        bind:change="nameChange" bind:search="onSearch">
        <view class="right-icon z-font-22 z-flex" slot="right-icon" bind:tap="onSearch">搜索</view>
      </van-search>
    </view>
    <van-tabs wx:if="{{cate.length > 1}}" line-width="0" title-active-color="#1782FA" title-inactive-color="#A4A9B7"
      bind:change="onChange">
      <van-tab wx:for="{{cate}}" wx:key="id" title="{{item.label}}"></van-tab>
    </van-tabs>

  </view>

  <view class="main-box">
    <scroll-view scroll-y class="shopbox z-padding-lr-32" show-scrollbar="{{false}}" enhanced="true" bindscrolltolower="lowerthreshold">
      <block wx:for="{{list}}"  wx:key="id">
        <shop info="{{item}}" bind:shopTap="shopTap"></shop>
      </block>
      <van-empty description="暂无商家" wx:if="{{finish && list.length === 0}}" />
    </scroll-view>
  </view>
</view>