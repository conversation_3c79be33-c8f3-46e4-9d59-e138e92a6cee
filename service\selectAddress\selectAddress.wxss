page {
  background-color: #EDF1F4;
}
.selectAddress{
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.search-box {
  flex-shrink: 0;
  width: 686rpx;
  height: 70rpx;
  background: #FFFFFF;
  border-radius: 44rpx 44rpx 44rpx 44rpx;
  opacity: 1;
  margin: auto;

  display: flex;
  align-items: center;
}

.city {
  font-size: 24rpx;
  font-weight: 500;
  color: #6B738B;
}

.cityico {
  width: 13rpx;
  height: 8rpx;
}

.rod {
  width: 0rpx;
  height: 24rpx;
  opacity: 1;
  border-right: 1rpx solid #A4A9B7;
}

.input {
  flex: 1;
}

.seek-btn {
  width: 90rpx;
  height: 58rpx;
  background: #1782FA;
  border-radius: 44rpx 44rpx 44rpx 44rpx;
  opacity: 1;
  font-size: 22rpx;
  line-height: 58rpx;
  font-weight: 500;
  color: #FFFFFF;
  margin-right: 6rpx;
}

.result-box {
  flex: 1;
  overflow-y: auto;
  width: 686rpx;

  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
  margin: auto;
}

.address {
  display: flex;
  align-items: center;
}

.lon-img-box {
  width: 46rpx;
  height: 30rpx;
}

.lon-img {
  width: 30rpx;
  height: 30rpx;
}

.each {
  border-bottom: 1rpx solid #EDEEF1;
  background: #FFFFFF;
}

.each:last-of-type {
  border: none;
}

.detail-address {
  font-size: 22rpx;
  font-weight: 500;
  color: #A4A9B7;
  padding-left: 46rpx;
}