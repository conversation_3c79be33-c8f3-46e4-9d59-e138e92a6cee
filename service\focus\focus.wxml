<!--service/focus/focus.wxml-->
<view class="focus">
  <van-nav-bar
    title="我的关注"
    left-arrow
    bind:click-left="onClickLeft"
    custom-style="background-color: #c8e2ff;"
    title-style="width: 136rpx; height: 36rpx; font-family: PingFangSC, PingFang SC; font-weight: 600; font-size: 34rpx; color: #333333; line-height: 36rpx; text-align: right; font-style: normal;"
  />
<view class="top-box">
  <van-tabs active="{{ active }}" color='#1782FA' title-inactive-color='#999' bind:change="onChange">
  <van-tab title="服务人员">

  </van-tab>
  <van-tab title="服务项目">

  </van-tab>
  <van-tab title="店铺">

  </van-tab>
</van-tabs>
</view>
<view class="main-box">
  <scroll-view scroll-y class="scroll-box z-padding-32" show-scrollbar="{{false}}" enhanced="true" bindscrolltolower="lowerthreshold">
      <block wx:for="{{list}}" wx:if="{{active == 0}}">
        <skill info="{{item}}" bind:skillTap="skillTap" bind:appoint="skillTap"></skill>
      </block>
      <block wx:for="{{list}}" wx:if="{{active == 1}}">
        <service info="{{item}}" type="{{2}}" bind:serviceTap="serviceTap"></service>
      </block>
      <block wx:for="{{list}}" wx:if="{{active == 2}}">
        <shop info="{{item}}" bind:shopTap="shopTap"></shop>
      </block>
      <van-empty description="暂无地址" wx:if="{{finish && list.length === 0}}" />
      
    </scroll-view>
</view>
</view>

