// service/servelist.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list:[],
    finish: false,
    loading: false,
    page: 1,
    goods_sku_id: '',
    goods_id: '',
    num: ''
  },
  appoint(e){
    util.skip('/service/makeorder/makeorder?goods_sku_id='+this.data.goods_sku_id+'&num='+this.data.num+'&goods_id='+this.data.goods_id+'&skill_id='+e.detail.id+'&shop_id='+this.data.shop_id)
  },
  reload(){
    this.setData({
      list: [],
      page: 1,
      finish: false
    })
    this.getList()
  },
  more(){
    this.setData({
      page: ++this.data.page
    })
    this.getList()
  },
  getList(){
    if(this.data.finish){
      return 
    }
    if(this.data.loading){
      return 
    }
    this.setData({
      loading: true
    })
    http.get('skill/searchskill', {
      city: app.globalData.address.city,
      lat: app.globalData.address.lat,
      lng: app.globalData.address.lng,
      page: this.data.page,
      goods_id: this.data.goods_id,
      shop_id: this.data.shop_id
    }).then(res => {
      if(res.data.length === 0){
        this.setData({
          finish: true
        })
      }
      let arr = this.data.list.concat(res.data)
      this.setData({
        list: arr
      })
      
    }).finally(res => {
      this.setData({
        loading: false
      })
    })
  },
  onLoad(options) {

    this.setData({
      goods_id: options.goods_id,
      num: options.num,
      goods_sku_id: options.goods_sku_id,
      shop_id: options.shop_id || ''
    })
    this.reload()
  },

})