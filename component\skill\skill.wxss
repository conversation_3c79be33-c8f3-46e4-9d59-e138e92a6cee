/* component/skill/skill.wxss */
@import '../../common.wxss';
.skillbox_item{
  background-color: #fff;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  position: relative;
}
.rest{
  position: absolute;
  right: 0;
  top: 0;
  height: 84rpx;
  width: 142rpx;
  display: flex;
  justify-content: flex-end;
}

.skillbox_item_right{
  
  position: absolute;
  right: 0;
  top: 0;
  height: 84rpx;
  width: 267rpx;
  display: flex;
  justify-content: flex-end;
}

.status-icon{
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}
.status{
  z-index: 10;
  position: relative;
}
.skillbox_item_right .status{
  color: var(--maingreencolor);
}

.skillbox_item_img{
  width: 153rpx;
  height: 153rpx;
}
.skillinfo{
  display: flex;
  /* align-items: center; */
  flex-direction: column;
  width: calc(686rpx - 180rpx);
}
.skill_tips{
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.skill_tips_left{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.skill_tips_left_item{
  background-color: var(--mainbg);
  height: 32rpx;
}
.skill_tips_left_item:nth-child(n+2){
  margin-left: 20rpx;
}
.shop_img{
  width: 25rpx;
  height: 25rpx;
}
.shop_name{
  color: #DBAC54;
  height: 32rpx;
  background-color: #FCF7EE;
}
.shop{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.choose{
  width: 124rpx;
  height: 48rpx;
  background: #1782FA;
  color: #fff;
}