@import "../../common.wxss";

page {
  background-color: #FFF;
}

.head {
  width: 750rpx;
  display: flex;
  position: absolute;
  top: 1rpx;
  z-index: 9;
}

.fanhui {
  width: 48rpx;
  height: 48rpx;
  position: absolute;
  left: 28rpx;
}

.fanhui-img {
  width: 48rpx;
  height: 48rpx;
}

.head-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #FFFFFF;

  position: absolute;
  left: 50%;
  transform: translate(-50%);
}

.bac-img {
  width: 750rpx;
  height: 463rpx;
}

.yue {
  width: 686rpx;
  height: 265rpx;
  background: linear-gradient(90deg, #D4FDDD 0%, #B9F0C9 100%);
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  opacity: 1;
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  bottom: 0;
  display: flex;
  justify-content: space-between;
}

.head-box {
  width: 750rpx;
  height: 463rpx;
  position: relative;
}

.yu-text {
  color: #3D8352;
  font-size: 26rpx;
}

.money {
  font-size: 48rpx;
  margin-top: 10rpx;
  font-weight: bold;
  color: #3D8352;
  line-height: 56rpx;
}

.detail-btn {
  background: #FFFFFF;
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  opacity: 1;
  width: 137rpx;
  height: 56rpx;
  background: #FFFFFF;
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  opacity: 1;
  line-height: 56rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #1782FA;
}

.gold {
  width: 317rpx;
  height: 250rpx;
  margin-top: 16rpx;
  margin-right: 37rpx;
}


.title {
  font-size: 30rpx;
  font-weight: bold;
  color: #1C274C;
}

.money-box {
  flex-wrap: wrap;
}

.each-money {
  width: 213rpx;
  /* height: 114rpx; */
  padding: 24rpx;
  background: #F5F7F9;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  opacity: 1;
  /* line-height: 114rpx; */
  font-size: 22rpx;
  font-weight: bold;
  color: #1C274C;
  border: 2rpx solid #FFF;
  margin: 0 18rpx 18rpx 0;
  box-sizing: border-box;
}

.each-money:nth-of-type(3n){
  margin-right: 0;
}

.cur-each-money {
  background: #F0FAF3;
  border: 2rpx solid #1782FA;
  font-weight: bold;
  color: #1782FA;
}

.input {
  width: 622rpx;
  background: #F5F7F9;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  opacity: 1;
  margin: auto;
}

.btn {
  width: 686rpx;
  height: 98rpx;
  background: #1782FA;
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  opacity: 1;
  font-size: 30rpx;
  line-height: 98rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin: auto;
  margin-top: 108rpx;
}

.agreement-box {
  width: 422rpx;
  margin: auto;
}

.choose_img {
  width: 32rpx;
  height: 32rpx;
  vertical-align: middle;
  margin-right: 16rpx;
}

.t-color {
  font-size: 26rpx;
  font-weight: 500;
  color: #FF9600;
}