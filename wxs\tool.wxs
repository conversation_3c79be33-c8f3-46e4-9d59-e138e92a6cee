
var cdnurl = 'https://service.infooi.cn' //图片cdn域名
var domain = 'https://service.infooi.cn'  //项目域名

var cdn = function (url, type) {
  if (!url) {
    return ''
  }
  if (typeof url === 'string') {
    if (url.indexOf('http') === 0) {
      return url
    } else {
      return (type ? domain :cdnurl )+ url
    }
  } else {
    var arr = []
    for (var i = 0; i < url.length; i++) {
      if (url.indexOf('http') === 0) {
        arr.push(url[i])
      } else {
        arr.push((type ? domain :cdnurl ) + url[i])
      }
    }
    return arr
  }
}

 var skip = function (event){
   console.log(2333)
}

var split = function (str, sym = ',') {
  if(typeof str === 'object' ){
    return str
  }else if(str === ''){
    return []
  }
  return str.split(sym)
}

var join = function (arr, sym = ',') {
  return arr.join(sym)
}

var toFix = function (num, digit = 2){
  var num = parseFloat(num)
  return num.toFixed(digit)
}

var includes = function (array, searchElement) {
  return array.indexOf(searchElement) !== -1
}



var formatTime = function (time, format = 'yyyy-mm-dd HH:MM') {
  if(time.toString().length == 13){
    var date = getDate(time);
  }else{
    var date = getDate(time * 1000);
  }
  var year = date.getFullYear()
  var month = formatNumber(date.getMonth() + 1)
  var day = formatNumber(date.getDate())
  var hour = formatNumber(date.getHours())
  var minute = formatNumber(date.getMinutes())
  var second = formatNumber(date.getSeconds())

  format = format.replace('yyyy',year)
  format = format.replace('mm',month)
  format = format.replace('dd',day)
  format = format.replace('HH',hour)
  format = format.replace('MM',minute)
  format = format.replace('ss',second)

  return format
}

var formatNumber = function(n) {
  n = n.toString()
  return n[1] ? n : '0'+n
}


var getHours = function(time){
  if(time.toString().length == 13){
    var date = getDate(time);
  }else{
    var date = getDate(time * 1000);
  }
  var hour = date.getHours()
  return hour
}

var parInt = function(num){
  return parseInt(num)
}

var full = function(num){
  var num = toFix(num,1)
  var digit = num.toString().split('.')[1]
  return digit == 0
}

module.exports = {
  cdn: cdn,
  skip: skip,
  split: split,
  join: join,
  toFix: toFix,
  includes: includes,
  formatTime: formatTime,
  getHours: getHours,
  parInt: parInt,
  full: full
}
