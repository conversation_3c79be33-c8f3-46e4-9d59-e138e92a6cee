// service/focus/focus.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    active:0,
    mylists: [{
      id: 1,
      name: '推荐服务'
    },{
      id: 1,
      name: '推荐服务'
    }],
    mylist:[{},{}]
  },
  shopTap(e){
    util.skip('/service/shopdetail/shopdetail?shop_id='+e.detail.id)
  },
  //跳转服务者详情
  skillTap(e){
    util.skip('/service/skilldetail/skilldetail?skill_id='+e.detail.id)
  },
  serviceTap(e){
    util.skip('/service/servedetail/servedetail?id='+e.detail.id)
  },
  onClickLeft(){
    util.back()
  },
  onChange(e){
    this.setData({
      active: e.detail.index
    })
    this.reload()
  },
  reload(){
    this.setData({
      list: [],
      page: 1,
      finish: false
    })
    this.getList()
  },

  getList(){
    if(this.data.finish){
      return
    }
    if(this.data.loading){
      return
    }
    this.setData({
      loading: true
    })
    http.get('follow/getlist', {
      type: this.data.active,
      page: this.data.page,
      lng: app.globalData.address.lng,
      lat: app.globalData.address.lat,
    }).then(res => {
      if(res.data.length === 0){
        this.setData({
          finish: true
        })
      }

      let arr = this.data.list.concat(res.data)
      this.setData({
        list: arr
      })

    }).finally(res => {
      this.setData({
        loading: false
      })
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      active:Number(options.active)
    })
    wx.setNavigationBarTitle({
      title: options.name || '详情',
    })
    this.reload()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
