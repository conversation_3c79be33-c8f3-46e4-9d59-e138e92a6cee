// service/gocomment/gocomment.js
import http from "../../utils/http"
import util from "../../utils/util"
Page({
  /**
   * 页面的初始数据
   */
  data: {
    score: 5,
    content: '',
    pusharr:[],
    images:[],
    list: []
  },
  choose(e){
    let current = Number(e.currentTarget.dataset.id)
    if (this.data.pusharr.includes(current)) {
      this.data.pusharr = this.data.pusharr.filter(item => item != current)
    } else {
      this.data.pusharr.push(current)
    }
    this.setData({
      pusharr: this.data.pusharr
    })
  },
  setContent(e){
    this.setData({
      content: e.detail.value
    })
  },
  delImages(e){
    let index = e.currentTarget.dataset.index
    this.data.images.splice(index,1)
    this.setData({
      images: this.data.images
    })
  },
  onChange(event) {
    this.setData({
      score: event.detail,
    });
  },
  chooseImages(e) {
    let index = e.currentTarget.dataset.index
    http.chooseImg(['album', 'camera'], true, true).then(res => {
      let arr = this.data.images;
      if(index !== undefined){
        arr.splice(index,1,res.data.url)
      }else {
        arr.push(res.data.url)
      }
      this.setData({
        images: arr
      })
    })
  },
  save(){
    http.post('comment/commentadd',{
      order_id: this.data.order_id,
      content: this.data.content,
      images: this.data.images.join(','),
      score: this.data.score,
      comment_label_ids: this.data.pusharr.join(',')
    }).then(res => {
      util.toast(res.msg)
      setTimeout(()=>{
        util.back()
      },1000)
    })
  },
  getInfo(){
    http.post('comment/commentlabel').then(res => {
      this.setData({
        list: res.data
      })
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      order_id: options.order_id
    })
    this.getInfo()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})