
.mytop{
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}
.nav{
  display: flex;
  width: 686rpx;
  align-items: flex-end;
  border-radius: 15rpx;
}

.nav .item{
  text-align: center;
  line-height: 80rpx;
  flex: 1;
  height: 80rpx;
  background: #F6F8FA;
  border-radius: 15rpx 15rpx 0 0;
  position: relative;
  list-style: none;
}
.nav .item.active{
  height: 90rpx;
  background: #FFFFFF;
  line-height: 90rpx;
  font-weight: bold;
}
.nav .item:first-child:before{
  content: '';
  display: none;
  width: 20rpx;
  height: 100%;
  background: #FFFFFF;
  position: absolute;
  right: -10rpx;
  top: 0;
  z-index: 10;
  border-radius: 0 10rpx 0 0;
  transform: skew(10deg);
}
.nav .item:first-child.active:before{
  display: block;
}
.nav .item:last-child:before{
  content: '';
  display: none;
  width: 20rpx;
  height: 100%;
  background: #fff;
  position: absolute;
  left: -10rpx;
  top: 0;
  z-index: 10;
  border-radius: 10rpx 0 0 0;
  transform: skew(-10deg);
}
.nav .item:last-child.active:before{
  display: block;
}
.nav_content{
  background-color: #fff;
  width: 686rpx;
  box-sizing: border-box;
  padding: 20rpx;
  border-radius: 0 0 15rpx 15rpx ;
}
.plus{
  width: 32rpx;
  height: 32rpx;
}
.addaddress{
  display: flex;
  justify-content: center;
  align-items: center;
}
.addressname{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.addressname_left{
  display: flex;
}
.addressname_right{
  display: flex;
  color: var(--maingreencolor);
}
.shop_address{
  flex: 1;
  line-height: 40rpx;
}
.shop_name{
  background-color: #6116FF;
  color: #fff;
}
.avatar{
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}
.servetime{
  display: flex;
  justify-content: space-between;
}
.time{
  display: flex;
  align-items: center;
}
.goodsinfo{
  width: 686rpx;
  margin: 0 auto;
  background-color: #fff;
  box-sizing: border-box;
}
.goodsimg{
  width: 180rpx;
  height: 180rpx;
  
}
.goodsinfo_top{
  display: flex;
  align-items: center;
}
.goodsinfo_top_right{
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 180rpx;
  width: calc(686rpx - 180rpx);
  justify-content: space-between;
}
.goodsinfo_top_right_top{
  display: flex;
  align-items: center;
}
.tip{
  color: #F7C566;
  background-color: #1C274C;
  width: 48rpx;
  height: 28rpx;
  border-radius: 4rpx;
  flex-shrink: 0;
}
.tips_box{
  display: flex;
  flex-wrap: wrap;
}
.tips_item{
  display: flex;
  background-color: #F5F7F9;
}
.tips_item:nth-child(n+2){
  margin-left: 20rpx;
}
.goodsinfo_top_right_bottom{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.goodsinfo_top_right_bottom_left{
  display: flex;
  align-items: flex-end;
}
.goodsinfo_top_right_bottom_left_left{
  display: flex;
  color:var(--mainmoneycolor);
}
.van-stepper--round .van-stepper__minus {
  background-color: #1782FA !important;
  color: #fff !important;
  border: 1px solid #fff !important;
}
.van-stepper--round .van-stepper__plus {
  background-color: #1782FA !important;
  color: #fff;
}
.yhq_box{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.yhq_box_left{
  display: flex;
  align-items: center;
}
.yhq_img{
  width: 30rpx;
  height: 26rpx;
}
.yhq_box_right{
  display: flex;
  align-items: center;
  color: #A4A9B7;
}
.money{
  color:var(--mainmoneycolor);
}
.memo{
  font-size: 28rpx;
  width: auto!important;
  height: 200rpx!important;
  background: #F9FAF9;
  border-radius: 10rpx;
  padding: 32rpx;


  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 32rpx;
  color: rgba(0,0,0,0.3);
  line-height: 40rpx;
  text-align: left;
  font-style: normal;
}

.goodsinfo_bottom{
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.goodsinfo_bottom_money{
  display: flex;
  align-items: center;
}
.order_bz{
  width: 686rpx;
  margin:  0rpx auto;
  background-color: #fff;
  box-sizing: border-box;
}
.wx_img{
  width: 40rpx;
  height: 40rpx;
}
.left{
  display: flex;
  align-items: center;
}
.pay_box{
  width: 686rpx;
  margin: 0 auto;
  background: #fff;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.right{
  width: 32rpx;
  height: 32rpx;
  border: 1rpx solid #999;
  border-radius: 50%;
}
.choosed_img{
  width: 32rpx;
  height: 32rpx;
}
.bottom{
  flex-shrink: 0;
}
.order_bottom{
  width: 100vw;
  height: 200rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  justify-content: space-between;
  
}

.order_bottom_money{
  display: flex;
  align-items: center;
  color: var(--mainmoneycolor);
}
.order_bottom_right{
  width: 400rpx;
}

.add{
  width: 32rpx;
  height: 32rpx;
}
.makeorder{
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.main-box{
  overflow: auto;
  flex: 1;
}
.main-box::-webkit-scrollbar{
  display: none;
}
.levelBox{
  background: #1C274C;
  height: 70rpx;
}
.icon{
  width: 30rpx;
  height: 30rpx;
}
.level-info{
  color: #F7C566;
}

.travel-box{
  background: #fff;
}
.travel{
  width: 144rpx;
  height: 56rpx;
  background: #EDEEF1;
}

.choosed{
  background: #1782FA;
  color: #fff;
}



.day-t,.day-b{
  color: #A4A9B7;
}
.today .day-t,.today .day-b{
  color: #1C274C;
}

.times-box{
  height: 550rpx;
  overflow-y: auto;
}

.times{
width: 154rpx;
height: 130rpx;
justify-content: center;
margin-right: 23rpx;
background: #F5F7F9;
margin-bottom: 24rpx;
display: inline-block;
}
.times:nth-of-type(4n){
  margin-right: 0;
}

.times-b{
  color: #1782FA;
}
.active{
  background-color: #FFF7EB;
}
.active .times-t,.active .times-b{
  color: #FFC675;
}

.disable .times-t,.disable .times-b{
  color: #A4A9B7;
}
.times-t,.times-b{
  text-align: center;
  margin-top: 10rpx;
}


.choosed{
  background: #1782FA;
}
.choosed .times-t,.choosed .times-b{
  color: #fff;
}


.discount{
  height: 32rpx;
  background: #2C2B31;
  color: #F6E0B9;
  border-radius: 5rpx;
}

.notime{
  margin: 0 auto;
}

/* 导航栏样式 */
.van-nav-bar {
  background-color: #c8e2ff !important;
  z-index: 999;
}

.van-nav-bar__title {
  font-family: PingFangSC, PingFang SC !important;
  font-weight: 600 !important;
  font-size: 34rpx !important;
  color: #333333 !important;
  text-align: center !important;
  font-style: normal !important;
}

.van-nav-bar__arrow {
    color: #333333 !important;
    font-size: 44rpx !important;
    vertical-align: middle;
}

.van-nav-bar .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

.van-nav-bar__left .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

/* 立即支付按钮样式 */
.van-button--primary {
  background-color: #1782fa !important;
  border-color: #1782fa !important;
}

/* 立即支付按钮样式 */
.van-button--primary {
  background-color: #1782fa !important;
  border-color: #1782fa !important;
  border-radius: 50rpx !important;
}

.title{
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 32rpx;
  color: rgba(0,0,0,0.8);
  line-height: 48rpx;
  text-align: left;
  font-style: normal;
}