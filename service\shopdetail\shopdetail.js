
// service/shopdetail/shopdetail.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    active:0,
    info: '',
    list:[],
    finish: false,
    loading: false,
    page: 1,
    shop_id: ''
  },
  collect(){
    http.get('follow/handlefollow', {
      follow_id: this.data.shop_id,
      type: 2,
      state: this.data.info.followState ? 0 : 1
    }).then(res => {
      this.data.info.followState = this.data.info.followState ? 0 : 1
      this.setData({
        info: this.data.info
      })
    })
  },
  couponTap(e){
    
    if(e.detail.type == 1){
      http.get('coupon/receive', {
        type: 0,
        coupon_id: e.detail.info.id
      }).then(res => {
        util.toast(res.msg)
        this.reload()
      })
    }else if(e.detail.type == 2){
      this.setData({
        active:0
      })
    }
  },
  serviceTap(e){
    util.skip('/service/servedetail/servedetail?id='+e.detail.id+'&shop_id='+this.data.shop_id)
  },
  openLoaction(){
    wx.openLocation({
      latitude : Number(this.data.info.lat),
      longitude : Number(this.data.info.lng),
      name: this.data.info.address,
      scale: 18
    })
  },
  call(){
    wx.makePhoneCall({
      phoneNumber: this.data.info.leader_mobile
    })
  },
  toaptitude(){
    util.skip('/service/aptitude/aptitude?shop_id='+this.data.shop_id)
  },
  lowerthreshold2(){
    console.log('我触底了2')
  },
  lowerthreshold1(){
    console.log('我触底了1')
  },
  lowerthreshold(){
    console.log('我触底了')
  },
  onChange(event) {
    this.setData({
      active:event.detail.index
    })
    this.reload()
  },
  getInfo(){
    http.get('shop/shopdetail', {
      id: this.data.shop_id,
      page: 1
    }).then(res => {
      this.setData({
        info: res.data
      })
    })
  },
  reload(){
    this.setData({
      list: [],
      page: 1,
      finish: false
    })
    this.getList()
  },

  getList(){
    if(this.data.finish){
      return 
    }
    if(this.data.loading){
      return 
    }
    this.setData({
      loading: true
    })
    let url;
    if(this.data.page === 2 && this.data.active === 0){
      this.setData({
        finish: true,
        loading: false
      })
      return
    }
    if(this.data.active === 0) {
      url = 'shop/searchShopGoods'
    }else if(this.data.active === 1) {
      url = 'coupon/getlist'
    }else if(this.data.active === 2) {
      url = 'comment/getlist'
    }
    http.get(url, {
      page: this.data.page,
      shop_id: this.data.shop_id
    }).then(res => {
      if(res.data.length === 0){
        this.setData({
          finish: true
        })
      }
      let arr = this.data.list.concat(res.data)
      this.setData({
        list: arr
      })
      
    }).finally(res => {
      this.setData({
        loading: false
      })
    })
  },
  onLoad(options) {
    this.setData({
      shop_id: options.shop_id
    })
    this.getInfo();
    this.reload();
  },

  
})