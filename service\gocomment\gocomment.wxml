<!--service/gocomment/gocomment.wxml-->
<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="rate_box z-radius-24 z-padding-32 z-margin-32">
  <view>评价</view>
  <van-rate value="{{ score }}" bind:change="onChange" color='#FF9600' void-icon="star" void-color="#F6EEE1" />
</view>
<view class="choosetips z-font-28 z-margin-16">
  <view class="z-font-24 {{ tool.includes(pusharr,item.id)?'active':'choosetips_item' }}" bind:tap="choose" data-id="{{item.id}}" wx:for="{{list}}" wx:key="id">{{item.name}}</view>
</view>
<view class="z-margin-32">
  <view class="smcontent z-radius-24 z-padding-32 z-margin-tb-32">
    <textarea  class="content" v-model="content" bindinput="setContent" placeholder="请输入您的评价" />
    <view class="box">
    <view class="images-box" wx:for="{{images}}" data-index="{{index}}" bindtap="chooseImages">
      <image class="icon" src="{{tool.cdn(item)}}" mode="aspectFill"></image>
      <image src="/static/service/dels.png" class="del" catchtap="delImages" data-index="{{index}}"></image>
    </view>
    <view class="images-box" wx:if="{{images.length<9}}" bindtap="chooseImages">
      <image src="/static/service/upload.png" class="icon"></image>
    </view>
  </view>
  </view>
</view>
<view class="z-btn z-margin-lr-32 z-margin-tb-100" bindtap="save">提交评价</view>