// service/skilldetail/skilldetail.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    current: 0,
    alllength: 1,
    mylist:[{},{},{},{}],
    value:5,
    skill_id: '',
    info: '',
    num: '',
    list: []
  },
  previewImages(e){
    util.previewImage(this.data.info.images, e.currentTarget.dataset.index)
  },
  skillAuth(){
    util.skip('/service/skillauth/skillauth?skill_id='+this.data.skill_id)
  },
  shopTap(){
    util.skip('/service/shopdetail/shopdetail?shop_id='+this.data.info.shop_id)
  },
  collect(){
    if(!wx.getStorageSync('token')) {
      util.skip('/service/login/login')
      return
    }
    http.get('follow/handlefollow', {
      follow_id: this.data.skill_id,
      type: 0,
      state: this.data.info.followState ? 0 : 1
    }).then(res => {
      this.data.info.followState = this.data.info.followState ? 0 : 1
      this.setData({
        info: this.data.info
      })
    })
  },
  serviceTap(e){
    
    util.skip('/service/servedetail/servedetail?id='+e.detail.id+'&skill_id='+this.data.skill_id)
  },
  lowerthreshold(){
    console.log('我到底了')
  },
  bindchange(event) {
    this.setData({
      current: event.detail.current
    })
  },
  comment(){
    util.skip('/service/comments/comments?type=skill&id='+this.data.skill_id)
  },
  getInfo(){
    http.get('skill/skillinfo', {
      id: this.data.skill_id,
      lng: app.globalData.address.lng,
      lat: app.globalData.address.lat,
    }, true).then(res => {
      let eduArr = ['小学文化', '初中', '高中', '大专', '本科', '硕士', '博士']
      res.data.edu = eduArr[res.data.edu]
      res.data.images = res.data.images ? res.data.images : []
      res.data.images.unshift(res.data.image)
      this.setData({
        info: res.data
      })
    })
    http.get('comment/getlist', {
      skill_id: this.data.skill_id,
    }).then(res => {
      this.setData({
        list: res.data,
      })
    })
    http.get('comment/totalcomment', {
      skill_id: this.data.skill_id,
      type: 'skill'
    }).then(res => {
      this.setData({
        num: res.data
      })
    })
  },
  onLoad(options) {
    this.setData({
      skill_id: options.skill_id
    })
    this.getInfo()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})