const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    current: 0,
    iswx: 1,
    goods_id: '',
    goods_sku_id: '',
    num: 1,
    skill_id: '',
    sku: '',
    price: '',
    money: '',
    discount: '',
    timeInfo: '',
    coupon: '',
    to_shop: 1,
    paytype: 0,
    address: '',
    sumprice: '',
    traveltype: 0,
    memo: '',
    choose_skill_type: '',
    travel: '',
    totalMoney: '',
    info: '',
    shop: '',
    userInfo: '',
    show: false,
    day: 0,
    now: new Date().getTime(),
    dayTime: [],
    year: '',
    list: [],
    shop_id: '',
    safeBottom: `30px`,
    today: 0,
    skillInfo: '',
    remain: '',
    name: ''
  },
  onClose() {
    this.setData({
      show: false
    })
  },

  repay() {
    if (this.data.current == 0 && !this.data.address) {
      util.toast('请添加地址')
      return
    }
    wx.requestSubscribeMessage({
      tmplIds: [app.globalData.templateconfig.user_notice_template, app.globalData.templateconfig.user_order_template, app.globalData.templateconfig.order_finish_template],
      success: (res) => {
        console.log(res)
      },
      fail: (e) => {
        console.log(e)
      },
      complete: (res) => {
        this.pay()
      }
    })

  },
  pay() {
    let data = {
      package_order_detail_id: this.data.package_order_detail_id,
      num: this.data.num,
      memo: this.data.memo,
      address_id: this.data.address ? this.data.address.id : '',
      city: this.data.address.city,
      starttime: this.data.timeInfo.starttime,
    }

    http.post('package/createServiceOrder', data, true).then(res => {
      wx.redirectTo({
        url: '/service/result/result?type=1'
      })
    })
  },
  setMemo(e) {
    this.setData({
      memo: e.detail.value
    })
  },
  numChange(e) {
    if(Number(e.detail)<=Number(this.data.remain)){
      this.setData({
        num: e.detail
      })
    }else{
      this.setData({
        num: Number(this.data.remain)
      })
    }
   
  },

  //跳转地址管理
  toaddress() {
    util.skip('/service/addressmanager/addressmanager?type=1', {
      selectAddress: (data) => {
        this.setData({
          address: data
        })
        this.getTravel()
      }
    })
  },

  choose(e) {
    let time = e.currentTarget.dataset.info
    this.setData({
      timeInfo: {
        starttime: time.starttime,
        id: time.id
      }
    })
    this.getTravel()
  },
  chooseTime() {
    this.setData({
      show: true
    })
  },

  changeDay(e) {
    this.setData({
      day: Number(e.currentTarget.dataset.index)
    })
    this.setData({
      list: []
    })
    this.getTime()
  },
  getTime() {
    let day = this.data.year + '-' + this.data.dayTime[this.data.day].time + ' 00:00:00'
    let start = new Date(day).getTime() / 1000
    let end = start + 24 * 60 * 60 - 1
    for (let i = start; i < end; i += 1800) {
      this.data.list.push({
        starttime: i,
        state: 0,
        id: i
      })
    }
    this.setData({
      list: this.data.list
    })

  },
  getInfo() {
    http.get('goods/goodsInfo', {
      id: this.data.goods_id,
    }).then(res => {
      let timeInfo;
      
      let time = new Date().getTime() + res.data.response_hour * 60 * 60 * 1000 - 1 * 60 * 1000;
      let date = new Date(time);
      let d = date.getDate();
      let h = date.getHours();
      let m = date.getMinutes();
      date.setSeconds(0)
      if (m <= 30) {
        date.setMinutes(30)
      } else if (m > 30) {
        date.setMinutes(0)
        date.setHours(h + 1)

      }
      if (date.getHours() >= res.data.end_hour) {
        date.setDate(d + 1)
        date.setHours(res.data.start_hour)
        date.setMinutes(0)
        this.setData({
          day: 1,
          today: 1
        })
      } else if (date.getHours() < res.data.start_hour) {
        date.setHours(res.data.start_hour)
        date.setMinutes(0)
      }

      this.setData({
        now: Date.parse(date) / 1000
      })
      timeInfo = {
        starttime: Date.parse(date) / 1000,
        id: ''
      }
      this.setData({
        info: res.data,
        timeInfo: timeInfo
      })
      this.getTime()
      http.get('address/addresslist', {
        page: 1,
      }).then(res => {
        this.setData({
          address: res.data[0]
        })
      })
    })

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let now = new Date().getTime();
    const high = app.globalData.safeBottom
    this.setData({
      safeBottom: `${high}px`
    })
    this.setData({
      goods_id: options.goods_id,
      package_order_detail_id: options.package_order_detail_id,
      remain: options.remain,
      name: options.name,
      dayTime: [{
          day: '今天',
          time: util.formatTime(now, 'mm-dd')
        },
        {
          day: '明天',
          time: util.formatTime(now + 86400000, 'mm-dd')
        },
        {
          day: '后天',
          time: util.formatTime(now + 172800000, 'mm-dd')
        }
      ],
      year: util.formatTime(now, 'yyyy')
    })
    this.getInfo()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})