// index.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({
  data: {
    thumb: [],
    cate: [],
    cateGoods:[],
    height: '',
    menuH: '32px',
    safeTop: `40px`,
    address: '',
    nearSkill: [],
    config: '',
    show:true,
    showPopup: false
  },
  toPage(){
    util.skip(this.data.config.home_url)
  },
  hidePopup() {
    this.setData({
      showPopup: false
    });
  },

  tosetMealDetail(){
    wx.navigateTo({
      url: '/service/setMealDetail/setMealDetail',
    })
  },
  onClose() {
    this.setData({
      show: false
    });
  },
  selectAddress(){
    util.skip('/service/selectAddress/selectAddress?city='+this.data.address.city+'&name='+this.data.address.name)
  },
  search(){
    util.skip('/service/searchdetail/searchdetail?type=1')
  },
  nearSkill(){
    wx.switchTab({
      url: '/pages/servers/servers'
    })
  },
  cateList(e){
    let name = e.currentTarget.dataset.item.label ? e.currentTarget.dataset.item.label : e.currentTarget.dataset.item.name
    util.skip('/service/catelist/catelist?category_id='+e.currentTarget.dataset.item.id+'&name='+name)
  },
  swiperTap(e) {
      let item = e.currentTarget.dataset.item;
      if(item.type == 1){
        util.skip('/service/servedetail/servedetail?id='+item.goods_id)
      }else if(item.type == 2){
        util.skip('/service/info/info?id='+item.config_text_id)
      }else if(item.type == 3){
        util.skip(item.jump)
      }

  },
  center() {
    util.authSkip('/service/couponscenter/couponscenter')
  },
  //跳转服务者详情
  skillTap(e){
    util.skip('/service/skilldetail/skilldetail?skill_id='+e.currentTarget.dataset.info.id)
  },
  serviceTap(e){
    util.skip('/service/servedetail/servedetail?id='+e.detail.id)
  },
  
  // 分类点击事件
  categoryTap(e) {
    const item = e.currentTarget.dataset.item;
    let name = item.label ? item.label : item.name;
    util.skip('/service/catelist/catelist?category_id=' + item.id + '&name=' + name);
  },
  
  // 全部服务点击事件
  goToClassify() {
    wx.switchTab({
      url: '/pages/classify/classify'
    });
  },


  // 事件处理函数
  bindViewTap() {
    wx.navigateTo({
      url: '../logs/logs'
    })
  },

  totypedetail(e) {
    wx.navigateTo({
      url: '/service/tentype/tentype?name=' + e.currentTarget.dataset.name,
    })
  },

  go() {
    wx.navigateTo({
      url: '/service/addressmanager/addressmanager',
    })
  },


  getInfo(){
    http.post('thumb/getlist', '', true).then(res => {
      this.setData({
        thumb: res.data
      })
    })


    http.post('category/getlist', '', true).then(res => {
      this.setData({
        cate: res.data
      })
    })



  },
  getCityInfo(){
    http.get('skill/nearskill', {
      city: this.data.address.city,
      lat: this.data.address.lat,
      lng: this.data.address.lng,
      page: 1
    }, false).then(res => {
      this.setData({
        nearSkill: res.data
      })
    })
    http.post('goods/categorygoods', {city: this.data.address.city}, false).then(res => {
      this.setData({
        cateGoods: res.data
      })
    })
  },
  onLoad(options) {
    console.log(wx.getLaunchOptionsSync())
    console.log(options)
    this.setData({
      safeTop: `${app.globalData.safeTop}px`,
      menuH: `${app.globalData.menuH}px`,
    })
    this.getInfo();

    let pages = getCurrentPages()

			let curpage = pages[pages.length - 1]
      let scene = decodeURIComponent(curpage.options.scene)

			const params = {};
			scene.split('&').forEach(item => {
				const [key, value] = item.split('=');
				params[key] = value;
      });
      console.log(params)
      if(params.leader_id){
        wx.setStorageSync('leader_id', params.leader_id)
      }
  },
  onShow(){

  },
  onLoadConfig(option){
    this.setData({
      config: app.globalData.config
    })
    if(this.data.config.home_image || this.data.config.home_content){
      this.setData({
        showPopup : true
      })
    }
  },
  onShowAddress(option){


    this.setData({
      address: app.globalData.address
    })
    this.getCityInfo()
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {}
  }
})
