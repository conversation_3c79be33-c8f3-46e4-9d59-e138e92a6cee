/* service/addnewaddress/addnewaddress.wxss */
page{
  position: relative;
  background-color: #fff;
}
.address{
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.main-box{
  overflow-y: auto;
  flex: 1;
}
.input_box{
  background-color: #F5F7F9;
}
.sex_box{
  display: flex;
  flex: 1;
  justify-content: space-around;
}
.sinput_box{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.men{
  display: flex;
  align-items: center;
}
.men{
  display: flex;
  align-items: center;
}
.greencircle{
  width: 32rpx;
  height: 32rpx;
}
.defaultaddress{
  width: 686rpx;
  margin: 0 auto;
}
.defaultaddress_top{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.sure{
  display: flex;
  align-content: center;
  justify-content: center;
  bottom: 100rpx;
  width: 100vw;
  flex-shrink: 0;
}
.btnbox{
width: 680rpx;
}