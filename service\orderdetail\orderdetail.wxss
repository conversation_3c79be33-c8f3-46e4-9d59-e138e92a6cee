/* service/orderdetail/orderdetail.wxss */
page {
  background: white;
}

/* 导航栏样式 */
.van-nav-bar {
  background-color: #c8e2ff !important;
  z-index: 999;
}

.van-nav-bar__title {
  font-family: PingFangSC, PingFang SC !important;
  font-weight: 600 !important;
  font-size: 34rpx !important;
  color: #333333 !important;
  text-align: center !important;
  font-style: normal !important;
}

.van-nav-bar__arrow {
    color: #333333 !important;
    font-size: 44rpx !important;
    vertical-align: middle;
}

.van-nav-bar .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

.van-nav-bar__left .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

.orderdetail{
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.main-box{
  flex: 1;
  overflow-y: auto;
}

.box_top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333333;
}

.title {
  font-size: 40rpx;
}

.time {
  color: #666666;
  line-height: 70rpx;
}
status-box{
  background: #fff;
}
.status-top{
  background: #1782FA;
  display: flex;
  align-items: center;
  height: 71rpx;
  border-radius: 20rpx;
  color: #fff;
}
.icon{
  margin-top: -8rpx;
  width: 524rpx;
  height: 71rpx;
}

.status-list{
  background: #fff;
  margin-top: -10rpx;
}
.list{
  display: flex;
  align-items: flex-start;
  position: relative;
}
.list::before{
  content: '';
  position: absolute;
  left: 38rpx;
  top: 50rpx;
  height: 140rpx;
  width: 1rpx;
  background-color: #EDEEF1;
}
.list:last-of-type::before{
  display: none;
}
.circle{
  width: 13rpx;
  height: 13rpx;
  border-radius: 50%;
  background-color: #EDEEF1;
  margin-top: 14rpx;
}

.order_content {
  background-color: #f6f7f8;
  width: 100vw;
  border-radius: 30rpx 30rpx 0 0;
  margin: 30rpx auto 0;
  position: relative;
  box-sizing: border-box;
}

.order_content_time {
  background-color: #F5F7FB;
  height: 44rpx;
  width: 120rpx;
  color: #6B738B;
  border-radius: 6rpx;
}

.order_info {
  background-color: #fff;
  box-sizing: border-box;
}

.order_content_time_text {
  font-size: 40rpx;
}

.hour {
  color: var(--maingreencolor);
}

.my_box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.my_box_img {
  width: 32rpx;
  height: 32rpx;
}

.my_box_bz {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.my_box_bz_one {
  flex: 1;
}

.my_box_bz_two {
  flex: 3;
}

.sikll_box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 686rpx;
  margin: 0 auto;
  background-color: #fff;
  box-sizing: border-box;
}

.sikll_box_img {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
}

.sikll_box_right {
  display: flex;
  align-items: center;
}

.phone{
  width: 40rpx;
  height: 40rpx;
}

.shop_info {
  width: 686rpx;
  box-sizing: border-box;
  background-color: #fff;
}

.project {
  display: flex;
  align-items: center;
}

.project_img {
  width: 120rpx;
  height: 120rpx;
}

.project_right {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 120rpx;
  flex: 1;
}

.money {
  color: var(--mainmoneycolor);
}

.project_right_bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.coupons_box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.coupons_box_left {
  display: flex;
  align-items: center;
}

.yhq_img {
  width: 30rpx;
  height: 26rpx;
}

.all {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.orderinfo {
  width: 686rpx;
  margin: 0 auto;
  box-sizing: border-box;
  background-color: #fff;
}

.orderinfo_item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.copy {
  border: 1rpx solid var(--maingreencolor);
  color: var(--maingreencolor);
}

.order_bottom {
  width: 100vw;
  background-color: #fff;
  height: 200rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
}

.block {
  width: 100vw;
  height: 150rpx;
  background-color: #F6F8FA;
}

.warring_img {
  width: 40rpx;
  height: 40rpx;
}

.order_bottom_left {
  display: flex;
  align-items: center;
  flex-direction: column;
  
}

.order_bottom_right {
  display: flex;
  align-items: center;
}
.van-button{
  border-radius: 15rpx !important;
}
.ercode_box{
  width: 686rpx;
  background-color: #fff;
  margin: 0 auto;
  box-sizing: border-box;
}
.ercode_box_top{
  display: flex;
  align-items: center;
}
.ercode_box_img{
  width: 120rpx;
  height: 120rpx;
}
.fourbox_name_left{
  color: #F7C566;
  background-color: #1C274C;
  width: 48rpx;
  height: 28rpx;
  border-radius: 4rpx;
  flex-shrink: 0;
}
.ercode_box_top_right{
  display: flex;
  flex-direction: column;
  height: 120rpx;
  justify-content: space-between;
}
.ercode_box_top_right_item{
  display: flex;
  align-items: center;
}
.ercodeimg{
  width: 364rpx;
  height: 364rpx;
}
.newshop_info{
  background-color: #fff;
  width: 686rpx;
  margin: 0 auto;
  box-sizing: border-box;
}
.newshop_info_addressbox{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.line{
  width: 1rpx;
  height: 100rpx;
  background-color: #EDEEF1;
}
.newshop_info_addressbox_right{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.newshop_info_addressbox_item{
  display: flex;
  align-items: center;
  flex-direction: column;
}
.position{
  width: 40rpx;
  height: 40rpx;
}




.popbox{
  width: 686rpx;
  box-sizing: border-box;
}

.money{
  color: #FF9600;
}
.input_box{
  background-color: #F5F7F9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
input{
  text-align: right;
}
.popbox_bottom{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.popbox_bottom_money{
  display: flex;
  align-items: center;
}
.van-button{
  border-radius: 15rpx !important;
}
.plusbox{
  box-sizing: border-box;
}
.plus_item{
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #F5F7F9;
}
.plus_item_left{
  display: flex;
  flex-direction: column;
}


.discount{
  height: 32rpx;
  background: #2C2B31;
  color: #F6E0B9;
  border-radius: 5rpx;
}

.refund{
  color: #FF9600;
}