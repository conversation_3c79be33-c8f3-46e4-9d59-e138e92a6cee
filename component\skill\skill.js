// component/skill/skill.js
import util from "../../utils/util"
Component({
  /**
   * 组件的属性列表
   */
  properties: {
      info: Object
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    appoint(e){
      if(this.data.info.is_rest == 1){
        util.toast('休息中，暂不可约')
        return 
      }
      this.triggerEvent('appoint',this.data.info)
    },
    skillTap(){
      if(this.data.info.is_rest == 1){
        util.toast('休息中，暂不可约')
        return 
      }
      this.triggerEvent('skillTap',this.data.info)
    }
  }
})
