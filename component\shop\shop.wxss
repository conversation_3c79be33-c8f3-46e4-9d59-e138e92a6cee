/* component/shop/shop.wxss */
@import '../../common.wxss';
.shop_item{
  margin: 0 auto;
  background-color: #fff;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  position: relative;
}
.shop_img{
  width: 154rpx;
  height: 154rpx;
}
.shop_item_info{
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}
.shop_item_info_top{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.star{
  width: 24rpx;
  height: 24rpx;
}
.rate{
  color: var(--mainmoneycolor);
}
.shop_item_info_midea{
  display: flex;
  flex-wrap: wrap;
}
.shop_item_info_midea_item{
  height: 38rpx;
  line-height: 38rpx;
  margin-bottom: 8rpx;
  margin-right: 8rpx;
  background: #F5F7F9;
}


.position_img{
  width: 24rpx;
  height: 24rpx;
}


.shop-goods{
  border-top: 1px solid #EDEEF1;
  width: 100%;
  overflow: hidden;
}
.icon{
  width: 30rpx;
  height: 30rpx;
}
.price{
  color: #FF9600;
}
.more{
  width: 22rpx;
  height: 22rpx;
}