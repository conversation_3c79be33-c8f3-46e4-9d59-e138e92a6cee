// pages/classify/classify.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    height: '',
    safehigh: '',
    activeKey: 0,
    menuH: '32px',
    safeTop: `40px`,
    active:0,
    cate: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      safeTop: `${app.globalData.safeTop}px`,
      menuH: `${app.globalData.menuH}px`,
    })
    this.getInfo()
  },
  cateTap(e){
    util.skip('/service/catelist/catelist?two_category_id='+e.currentTarget.dataset.item.id+'&name='+e.currentTarget.dataset.item.label)
  },
  getInfo(){
    http.post('category/getlist', {type: 'all'}, true).then(res => {
      this.setData({
        cate: res.data
      })
    })
  },
  change(e){
    this.setData({
      active:e.currentTarget.dataset.index
    })
  },
  
  tosearchdetail(){
    wx.navigateTo({
      url: '/service/searchdetail/searchdetail',
    })
  },
  lowerthreshold(){
  },
  lookid(e){
    this.setData({
      active:e.currentTarget.dataset.id
    })
  },
   /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {}
  }
})