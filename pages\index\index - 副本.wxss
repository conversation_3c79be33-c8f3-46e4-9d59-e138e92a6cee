
page {
  background: #FFFFFF;
  height: 100%;
  width: 100%;
}  
.group_1 {
  padding: 20rpx;
  height: 325rpx;
  background-repeat: round;
  background-image: url(https://service.infooi.cn/uploads/20250727/78f34ced1ed8a66cc0a4a29023dbdd08.png);
  background-size: 750rpx 325rpx;
  display: flex;
  flex-direction: column;
}
.section_12 {
  flex-direction: row;
  display: flex;
  margin: 0 9rpx 0 24rpx;
}
.text_1 {
  overflow-wrap: break-word;
  color: rgba(0,0,0,1);
  font-size: 34rpx;
  font-family: DINAlternate-Bold;
  font-weight: 700;
  text-align: right;
  white-space: nowrap;
  line-height: 40rpx;
  margin-top: 4rpx;
}
.image_1 {
  width: 33rpx;
  height: 20rpx;
  margin: 14rpx 0 14rpx 459rpx;
}
.label_1 {
  width: 40rpx;
  height: 40rpx;
  margin: 4rpx 0 4rpx 12rpx;
}
.label_2 {
  width: 48rpx;
  height: 48rpx;
  margin-left: 12rpx;
}
.section_13 {
  margin-top: 90rpx;
}
.section_13 image{
  height: 44rpx;
  width:176rpx;
}
.text_2 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 44rpx;
  font-family: AlimamaShuHeiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 9rpx;
}
.image_2 {
  width: 168rpx;
  height: 62rpx;
}
.section_3 {
  box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.020000);
  background-color: rgba(255,255,255,1.000000);
  border-radius: 36rpx;
  display: flex;
  flex-direction: row;
  margin-top: 27rpx;
  padding: 18rpx 346rpx 18rpx 24rpx;
}
.image-text_19 {
  width: 332rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.label_19 {
  width: 36rpx;
  height: 36rpx;
}
.text-group_1 {
  overflow-wrap: break-word;
  color: rgba(153,153,153,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 6rpx;
}
.group_2 {
  background-color: rgba(255,255,255,1.000000);
  margin-top: 1491rpx;
  display: flex;
  flex-direction: column;
}
.box_1 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-around;
  padding: 12rpx 0 7rpx 0;
}
.image-text_20 {
  display: flex;
  flex-direction: column;
}
.label_20 {
  width: 48rpx;
  height: 48rpx;
}
.text-group_2 {
  overflow-wrap: break-word;
  color: rgba(23,130,250,1);
  font-size: 20rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 22rpx;
  align-self: center;
  margin-top: 9rpx;
}
.image-text_21 {
  display: flex;
  flex-direction: column;
}
.label_21 {
  width: 48rpx;
  height: 48rpx;
}
.text-group_3 {
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 20rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 22rpx;
  align-self: center;
  margin-top: 9rpx;
}
.image-text_22 {
  display: flex;
  flex-direction: column;
}
.label_22 {
  width: 48rpx;
  height: 48rpx;
}
.text-group_4 {
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 20rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 22rpx;
  align-self: center;
  margin-top: 9rpx;
}
.image-text_23 {
  display: flex;
  flex-direction: column;
}
.label_23 {
  width: 48rpx;
  height: 48rpx;
}
.text-group_5 {
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 20rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 22rpx;
  align-self: center;
  margin-top: 9rpx;
}
.image_3 {
  width: 750rpx;
  height: 68rpx;
}

.list_2 {
  height: 136rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
}
.image-text_24-0 {
  margin-right: 36rpx;
  display: flex;
  flex-direction: column;
}
.image_13-0 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}
.text-group_23-0 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}
.image-text_24-1 {
  margin-right: 36rpx;
  display: flex;
  flex-direction: column;
}
.image_13-1 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}
.text-group_23-1 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}
.image-text_24-2 {
  margin-right: 36rpx;
  display: flex;
  flex-direction: column;
}
.image_13-2 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}
.text-group_23-2 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}
.image-text_24-3 {
  margin-right: 36rpx;
  display: flex;
  flex-direction: column;
}
.image_13-3 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}
.text-group_23-3 {
  width: 120rpx;
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}
.image-text_24-4 {
  margin-right: 36rpx;
  display: flex;
  flex-direction: column;
}
.image_13-4 {
  width: 100rpx;
  height: 100rpx;
  align-self: center;
}
.text-group_23-4 {
  overflow-wrap: break-word;
  color: rgba(71,71,72,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 4rpx;
}
.section_4 {
  margin: 20rpx;
  border-radius: 16rpx;
  background-image: url(https://service.infooi.cn/uploads/20250727/11bad3c7f44bdf3fa175be4889dd0095.png);
  height: 262rpx;
  background-repeat: round;
  display: flex;
  flex-direction: column;
}
.box_9 {
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx;
}
.image-text_25 {
  width: 128rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.label_8 {
  width: 28rpx;
  height: 28rpx;
}
.text-group_7 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 1rpx;
}
.image-text_26 {
  width: 128rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.label_9 {
  width: 28rpx;
  height: 28rpx;
}
.text-group_8 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 1rpx;
}
.image-text_27 {
  width: 128rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.label_10 {
  width: 28rpx;
  height: 28rpx;
}
.text-group_9 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 1rpx;
}
.image-text_28 {
  width: 128rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.label_11 {
  width: 28rpx;
  height: 28rpx;
}
.text-group_10 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 1rpx;
}
.text_3 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 32rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-left: 25rpx;
}
.box_10 {
  flex-direction: row;
  display: flex;
  justify-content: space-around;
  margin: 20rpx;
}
.group_4 {
  background-repeat: round;
  background-image: url(https://service.infooi.cn/uploads/20250727/5deae86c56762c35302f8326d7f0c119.png);
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  padding: 30rpx 184rpx 21rpx 26rpx;
}
.text-group_24 {
  display: flex;
  flex-direction: column;
}
.text_4 {
  overflow-wrap: break-word;
  color: rgba(6,84,73,1);
  font-size: 30rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
  margin-right: 12rpx;
}
.paragraph_1 {
  width: 132rpx;
  height: 64rpx;
  overflow-wrap: break-word;
  color: rgba(6,84,73,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 32rpx;
  margin-top: 13rpx;
}
.block_1 {
  background-color: rgba(109,199,191,1.000000);
  border-radius: 17rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: row;
  padding: 2rpx 13rpx 2rpx 20rpx;
}
.image-text_29 {
  width: 99rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.text-group_12 {
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.label_24 {
  width: 26rpx;
  height: 26rpx;
  margin: 2rpx 0 2rpx 0;
}
.group_5 {
  background-repeat: round;
  background-image: url(https://service.infooi.cn/uploads/20250727/dcd220dc3187e357d7b707c72fd824af.png);
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  padding: 30rpx 184rpx 21rpx 26rpx;
}
.text-group_25 {
  display: flex;
  flex-direction: column;
}
.text_5 {
  overflow-wrap: break-word;
  color: rgba(14,60,90,1);
  font-size: 30rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
  margin-right: 12rpx;
}
.paragraph_2 {
  width: 132rpx;
  height: 64rpx;
  overflow-wrap: break-word;
  color: rgba(14,60,90,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 32rpx;
  margin-top: 13rpx;
}
.box_2 {
  background-color: rgba(111,164,198,1.000000);
  border-radius: 17rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: row;
  padding: 2rpx 4rpx 2rpx 12rpx;
}
.image-text_30 {
  width: 116rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.text-group_14 {
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.label_25 {
  width: 26rpx;
  height: 26rpx;
  margin: 2rpx 0 2rpx 0;
}
.box_11 {
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx;
}
.box_3 {
  background-size: 222rpx 176rpx;
  background-repeat: round;
  background-image: url(https://service.infooi.cn/uploads/20250727/464ec776144610c8896704ac6f91e778.png);
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  padding: 28rpx 18rpx;
}
.text_6 {
  overflow-wrap: break-word;
  color: rgba(84,58,7,1);
  font-size: 30rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
  margin-right: 84rpx;
}
.group_6 {
  margin-top: 13rpx;
  flex-direction: row;
  display: flex;
}
.box_12 {
  margin-bottom: 22rpx;
  display: flex;
  flex-direction: column;
}
.text_7 {
  overflow-wrap: break-word;
  color: rgba(84,58,7,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.box_6 {
  background-color: rgba(237,189,121,1.000000);
  border-radius: 17rpx;
  display: flex;
  flex-direction: row;
  margin: 17rpx 23rpx 0 0;
  padding: 2rpx 0 2rpx 10rpx;
}
.image-text_31 {
  width: 99rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.text-group_15 {
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.label_26 {
  width: 26rpx;
  height: 26rpx;
  margin: 2rpx 0 2rpx 0;
}
.image_5 {
  width: 96rpx;
  height: 105rpx;
  margin-left: -24rpx;
}
.box_7 {
  background-size: 222rpx 176rpx;
  background-repeat: round;
  background-image: url(https://service.infooi.cn/uploads/20250727/72b40ed5953be1a60f844571e147d36e.png);
  display: flex;
  border-radius: 16rpx;
  flex-direction: column;
  padding: 28rpx 18rpx;
}
.text-group_26 {
  display: flex;
  flex-direction: column;
}
.text_8 {
  overflow-wrap: break-word;
  color: rgba(85,9,28,1);
  font-size: 30rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
  margin-right: 64rpx;
}
.text_9 {
  overflow-wrap: break-word;
  color: rgba(85,9,28,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 13rpx;
}
.text-wrapper_1 {
  background-color: rgba(243,143,170,1.000000);
  border-radius: 17rpx;
  display: flex;
  flex-direction: column;
  margin: 17rpx 75rpx 0 0;
  padding: 2rpx 11rpx 2rpx 10rpx;
}
.text_10 {
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.box_8 {
  background-repeat: round;
  border-radius: 16rpx;
  background-size: 222rpx 176rpx;
  background-image: url(https://service.infooi.cn/uploads/20250727/030d3ad6f1b6e3b0299b9e9c797bf096.png);
  display: flex;
  flex-direction: column;
  padding: 28rpx 18rpx;
}
.text-group_27 {
  display: flex;
  flex-direction: column;
}
.text_11 {
  overflow-wrap: break-word;
  color: rgba(7,84,60,1);
  font-size: 30rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
  margin-right: 78rpx;
}
.text_12 {
  overflow-wrap: break-word;
  color: rgba(7,84,60,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 13rpx;
}
.block_2 {
  background-color: rgba(108,196,168,1.000000);
  border-radius: 17rpx;
  display: flex;
  flex-direction: row;
  margin: 17rpx 89rpx 0 0;
  padding: 2rpx 0 2rpx 10rpx;
}
.image-text_32 {
  width: 99rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.text-group_18 {
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.label_27 {
  width: 26rpx;
  height: 26rpx;
  margin: 2rpx 0 2rpx 0;
}
.text_13 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 32rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin: 20rpx;
}
.section_8 {
  margin-bottom: 150rpx!important;
  background-color: rgba(249,249,249,1.000000);
  border-radius: 16rpx;
  margin: 20rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  padding: 40rpx 26rpx;
}
.image-text_33 {
  display: flex;
  flex-direction: column;
}
.image_6 {
  width: 80rpx;
  height: 80rpx;
  align-self: center;
}
.text-group_19 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-top: 16rpx;
}
.label_28 {
  width: 24rpx;
  height: 24rpx;
  margin: 49rpx 0 49rpx 0;
}
.image-text_34 {
  display: flex;
  flex-direction: column;
}
.image_7 {
  width: 80rpx;
  height: 80rpx;
  align-self: center;
}
.text-group_20 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-top: 16rpx;
}
.label_29 {
  width: 24rpx;
  height: 24rpx;
  margin: 49rpx 0 49rpx 0;
}
.image-text_35 {
  display: flex;
  flex-direction: column;
}
.image_8 {
  width: 80rpx;
  height: 80rpx;
  align-self: center;
}
.text-group_21 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-top: 16rpx;
}
.label_30 {
  width: 24rpx;
  height: 24rpx;
  margin: 49rpx 0 49rpx 0;
}
.image-text_36 {
  display: flex;
  flex-direction: column;
}
.image_9 {
  width: 80rpx;
  height: 80rpx;
  align-self: center;
}
.text-group_22 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-top: 16rpx;
}


.section_9 {
  width: 212rpx;
  height: 145.96rpx;
  background: url(https://service.infooi.cn/uploads/20250727/f9607095d5a9ecc631323591a26e0441.png) 0rpx -1rpx no-repeat;
  background-size: 212rpx 145.96rpx;
  display: flex;
  flex-direction: column;
}

.section_10 {
  width: 212rpx;
  height: 145.96rpx;
  background: url(https://service.infooi.cn/uploads/20250727/9a77d186acb4757c4f148a846020c980.png) 0rpx -1rpx no-repeat;
  background-size: 212rpx 145.96rpx;
  display: flex;
  flex-direction: column;
}

.section_11 {
  width: 212rpx;
  height: 145.96rpx;
  background: url(https://service.infooi.cn/uploads/20250727/1258731fed43aa33e98ba60b2e9bf914.png) 0rpx -1rpx no-repeat;
  background-size: 212rpx 145.96rpx;
  display: flex;
  flex-direction: column;
}
