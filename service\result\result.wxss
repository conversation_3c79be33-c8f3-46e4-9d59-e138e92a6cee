/* service/result/result.wxss */
.result{
  height: 100vh;
  display: flex;
  align-items: center;
  flex-direction: column;
  background: #fff;
  box-sizing: border-box;
  padding-top: 200rpx;
}
.icon{
  width: 114rpx;
  height: 114rpx;
}

.result  .z-btn{
  width: 440rpx;
  height: 98rpx;
  margin-top: 160rpx;
  box-sizing: border-box;
}
.back{
  width: 440rpx;
  height: 98rpx;
  box-sizing: border-box;
  border: 1px solid #EDEEF1;
}

/* 导航栏样式 */
.van-nav-bar {
  background-color: #c8e2ff !important;
  z-index: 999;
}

.van-nav-bar__title {
  font-family: PingFangSC, PingFang SC !important;
  font-weight: 600 !important;
  font-size: 34rpx !important;
  color: #333333 !important;
  text-align: center !important;
  font-style: normal !important;
}

.van-nav-bar__arrow {
    color: #333333 !important;
    font-size: 44rpx !important;
    vertical-align: middle;
}

.van-nav-bar .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

.van-nav-bar__left .van-icon {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 44rpx !important;
}

