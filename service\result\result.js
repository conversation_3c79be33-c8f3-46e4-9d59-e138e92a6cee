// service/result/result.js
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    show: true,
    type: 1
  },
  back(){
    util.back()
  },
  onClickLeft(){
    util.back()
  },
  home(){
    wx.switchTab({
      url: '/pages/index/index'
    })
  },
  order(){
    if(this.data.type == 1){
      wx.switchTab({
        url: '/pages/myorder/myorder?type=1&active=2'
      })
    }else {
      util.back()
    }

  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad (options) {
    this.setData({
      type: options.type || 1
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})