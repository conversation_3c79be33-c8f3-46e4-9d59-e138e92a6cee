// service/myinfo/myinfo.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    avatar: '',
    nickname: '',
    mobile: '' // 新增手机号码字段
  },
  setName(e){
    this.setData({
      nickname: e.detail.value
    })
  },
  // 新增手机号码输入处理函数
  setMobile(e){
    this.setData({
      mobile: e.detail.value
    })
  },
  chooseAvatar(){
    http.chooseImg(['album', 'camera'], true).then(res => {
      this.setData({
        avatar: res.data.url
      })
    })
  },
  getInfo(){
    http.post('index/userindex', '').then(res => {
      this.setData({
        avatar: res.data.avatar,
        nickname: res.data.nickname,
        mobile: res.data.mobile || '' // 获取手机号码数据
      })
    })
  },
  onClickLeft(){
    util.back()
  },
  save(){
    http.post('index/updateuserinfo', {
      type: 'update',
      avatar: this.data.avatar,
      nickname: this.data.nickname,
      mobile: this.data.mobile // 新增手机号码参数
    }, true, false).then(res => {

      util.toast(res.msg)
    })
  },
  onLoad(options) {
    this.getInfo()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})