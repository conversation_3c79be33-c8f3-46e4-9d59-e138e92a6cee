// service/menber/menber.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    plus_id: '',
    agree: 0,
    plus: [],
    info: '',
    safeBottom: `padding-bottom:100px`
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const high = app.globalData.safeBottom
    this.setData({
      safeBottom: `padding-bottom:${high}px`
    })
    this.setData({
      mytime: util.formatTime(new Date(this.data.mytime))
    })
  },
  chose(e) {
    this.setData({
      plus_id: e.currentTarget.dataset.id
    })
  },
  readclick() {
    this.setData({
      agree: this.data.agree == 0 ? 1 : 0
    })
  },
  infoTap(e) {
    let info = e.currentTarget.dataset.info
    let name = e.currentTarget.dataset.name
    let id = app.globalData.config[info]
    util.skip('/service/info/info?id=' + id + '&name=' + name)
  },
  pay(e) {
    if(this.data.agree === 0) return util.toast('请阅读并同意《充值会员须知》')
    this.setData({
      plus_id: e.currentTarget.dataset.id
    })
    http.post('plus/plus', {
      type: 'pay',
      paytype: 0,
      id: this.data.plus_id
    }).then(res => {
      wx.requestPayment({
        timeStamp: res.data.timeStamp,
        nonceStr: res.data.nonceStr,
        package: res.data.package,
        signType: res.data.signType,
        paySign: res.data.paySign,
        success: (res) => {
          util.toast('支付成功')
          const eventChannel = this.getOpenerEventChannel()
          eventChannel.emit('changePlus');
          setTimeout(()=>{
            this.getInfo()
          },1000)

        },
        fail: (res) => {
          util.toast('支付失败')
        }
      })
    })
  },
  getInfo() {
    http.post('plus/plus').then(res => {
      this.setData({
        plus: res.data,
        plus_id: res.data[0].id
      })
    })
    http.post('index/userindex', '').then(res => {
      this.setData({
        info: res.data
      })
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getInfo()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})