/* pages/mytest/mytest.wxss */
.mybg{
  width: 100vw;
  position: absolute;
  height: 505rpx;
  z-index: 1;
}
.my_top{
  position: relative;
}
.avator{
  width: 118rpx;
  height: 118rpx;
}
.myinfo{
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  z-index: 1;
}
.avatar{
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
}
.myinfo_text{
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: 1;
}
.vip {
  width: 50rpx;
  height: 50rpx;
}
.namebox_text {
  display: flex;
  align-items: center;
}

.plusname{
  background: linear-gradient(90deg, #FFB40E 0%, #FF5942 100%);
  color: #fff;
  height: 46rpx;
  border-radius: 0 24rpx 24rpx 0;
  padding: 0 24rpx 0 40rpx;
  margin-left: -30rpx;
}
.vip{
  width: 50rpx;
  height: 50rpx;
  z-index: 10rpx;
  position: relative;
}

.menber{
  background-color: #fff;
}
.all_box{
  position: relative;
  margin: 50rpx 24rpx;
  width: calc(100% - 48rpx);
  /* box-sizing: border-box; */
  background-color: #fff;
  overflow: hidden;
}
.menber_box{
  background-color: #1C274C;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.menber_box_left{
  display: flex;
  align-items: flex-start;
}
.king_img{
  width: 44rpx;
  height: 44rpx;
}
.menber_box_text{
  display: flex;
  flex-direction: column;
}
.menber_text{
  color: #F7C566;
}
.poenit{
  background-color: #F7C566;
}
.fourbox{
  background-color: #fff;
  display: flex;
  align-items: center;
}
.fourboxitem{
  display: flex;
  position: relative;
  align-items: center;
  flex-direction: column;
  flex: 1;
  color: #383838;
}


.more{
  position: relative;
  width: 300rpx;
  height: 144rpx;
  background: #FFFBF0;
  box-sizing: border-box;
}
.more .num{
  color: #FF9600;
}
.more:last-of-type{
  background: #F0FAF3;
}



.icon{
  width: 80rpx;
  height: 80rpx;
  position: absolute;
  top: 30rpx;
  right: 24rpx;
}
.pos{
  position: relative;
  z-index: 10;
}

.order_box{
  background-color: #fff;
  width: 702rpx;
  margin: 0 auto;
  box-sizing: border-box;
  position: relative;
  z-index: 2;
  margin-top: 55rpx;
}
.order_box_top{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.num-box{
  position: relative;
  width: 48rpx;
  height: 48rpx;
}
.fourbox_img{
  width: 48rpx;
  height: 48rpx;
}
.point{
  position: absolute;
  color: #fff;
  background-color: #F22E2E;
  font-size: 18rpx;
  border-radius: 50%;
  width: 24rpx;
  height: 24rpx;
  text-align: center;
  right: -4rpx;
  top: -4rpx;
}
.setting_box{
  width: calc(100% - 48rpx);
  background-color: #fff;
  margin: 0 24rpx;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.setting_box_item{
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 25%;
  margin-bottom: 20rpx;
  position: relative;
}
.contact{
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
.setting_box_img{
  width: 48rpx;
  height: 48rpx;
}
.lastsetting{
  margin: 50rpx auto;
  width: 686rpx;
  height: 120rpx;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.lastsetting_left{
  display: flex;
  align-items: center;
}
.lastsetting_img{
  width: 40rpx;
  height: 40rpx;
}
.info-details{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.info-item1{
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 40rpx;
  color: #FFFFFF;
  line-height: 56rpx;
  text-align: left;
  font-style: normal;
}
.info-item2{
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 33rpx;
  text-align: left;
  font-style: normal;
}
.info-renzheng{
  margin-left: 10rpx;
  width: 116rpx;
  height: 32rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  color: #35B74F;
}

.logout{
  width: 702rpx;
  line-height: 110rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  text-align: center;
  box-sizing: border-box;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 30rpx;
  color: #333333;
  font-style: normal;
  margin:0  auto;
  margin-bottom: 100px;
}

/* 自定义cell样式 */
.custom-cell-group {
  padding: 32rpx;
  width: 100%;
}

.custom-cell-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
  position: relative;
}

.custom-cell-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.custom-cell-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}

.custom-cell-title {
  font-size: 30rpx;
  color: #333333;
  font-weight: 400;
}
